# Excel导出功能说明

## 🎯 功能概述

新增了2025年xqbmt_consult数据的Excel导出功能，支持：
- 导出2025年全年的咨询数据到Excel文件
- 按team_id分组统计各团队的数据量
- 按月份统计2025年新增数据趋势
- 提供详细的统计信息API

## 📊 API 接口

### 1. 导出Excel文件
```http
GET /api/export-consult-2025
```

**功能说明:**
- 导出2025年(2025-01-01 至 2025-12-31)的所有咨询数据
- 生成包含3个工作表的Excel文件
- 自动下载文件到浏览器

**响应:**
- 成功：返回Excel文件下载
- 失败：返回JSON错误信息

### 2. 获取统计数据
```http
GET /api/consult-stats-2025
```

**功能说明:**
- 获取2025年咨询数据的详细统计信息
- 不下载文件，只返回JSON数据

**响应示例:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalCount": 1250,
      "generatedCount": 890,
      "pendingCount": 360,
      "generatedPercentage": "71.20%",
      "year": "2025",
      "dateRange": {
        "start": "2025-01-01",
        "end": "2025-12-31"
      }
    },
    "teamStats": [
      {
        "teamId": "team_001",
        "totalCount": 450,
        "generatedCount": 320,
        "percentage": "36.00%",
        "generatedPercentage": "71.11%"
      }
    ],
    "monthlyStats": [
      {
        "month": "2025-01",
        "count": 120
      }
    ],
    "topTeams": []
  }
}
```

## 📋 Excel文件结构

### 工作表1: 2025年咨询数据
包含原始数据的详细信息：
- **ID**: 记录的唯一标识
- **Team ID**: 团队标识（未分组显示为"未分组"）
- **Open Status**: 开放状态
- **创建时间**: 记录创建日期
- **更新时间**: 记录更新日期
- **点击记录生成时间**: 点击记录的生成日期（未生成显示"未生成"）
- **点击记录数量**: 生成的点击记录数量

### 工作表2: Team统计
按team_id分组的统计信息：
- **Team ID**: 团队标识
- **数据条数**: 该团队的数据总量
- **占比**: 该团队数据占总数据的百分比
- **总计行**: 显示所有数据的汇总

### 工作表3: 月度统计
按月份统计的新增数据：
- **月份**: YYYY-MM格式
- **新增数据**: 该月新增的数据量

## 🔍 统计维度

### 总体统计
- **总数据量**: 2025年创建的所有咨询记录数
- **已生成点击记录**: 已经生成点击记录的咨询数量
- **待生成点击记录**: 尚未生成点击记录的咨询数量
- **生成完成率**: 已生成点击记录的百分比

### Team维度统计
- 按team_id分组统计各团队的数据量
- 计算每个团队的数据占比
- 统计每个团队的点击记录生成情况
- 按数据量降序排列

### 时间维度统计
- 按月份统计2025年各月的新增数据
- 显示数据增长趋势
- 便于分析业务发展情况

## 🧪 测试方法

### 1. 运行测试脚本
```bash
# 简单测试（只获取统计数据）
node test-excel-export.js

# 完整测试（包含Excel下载）
node test-excel-export.js full

# 只测试下载功能
node test-excel-export.js download
```

### 2. 手动测试
```bash
# 获取统计数据
curl http://localhost:3000/api/consult-stats-2025

# 下载Excel文件
curl -O -J http://localhost:3000/api/export-consult-2025
```

### 3. 浏览器测试
直接访问：
- 统计数据：`http://localhost:3000/api/consult-stats-2025`
- 下载Excel：`http://localhost:3000/api/export-consult-2025`

## 📝 创建测试数据

### 创建不同team_id的测试数据
```javascript
// Team A 的数据
db.xqbmt_consult.insertMany([
  {
    team_id: "team_001",
    open_status: 1,
    create_time: new Date("2025-01-15T10:30:00.000Z"),
    update_time: new Date()
  },
  {
    team_id: "team_001", 
    open_status: 1,
    create_time: new Date("2025-02-20T14:20:00.000Z"),
    update_time: new Date()
  }
])

// Team B 的数据
db.xqbmt_consult.insertMany([
  {
    team_id: "team_002",
    open_status: 1,
    create_time: new Date("2025-03-10T09:15:00.000Z"),
    update_time: new Date()
  },
  {
    team_id: "team_002",
    open_status: 1,
    create_time: new Date("2025-04-05T16:45:00.000Z"),
    update_time: new Date(),
    clicks_generated: new Date(),
    clicks_count: 456
  }
])

// 无team_id的数据（会显示为"未分组"）
db.xqbmt_consult.insertOne({
  open_status: 1,
  create_time: new Date("2025-05-12T11:30:00.000Z"),
  update_time: new Date()
})
```

### 查看现有数据
```javascript
// 查看2025年所有数据
db.xqbmt_consult.find({
  create_time: {
    $gte: new Date("2025-01-01T00:00:00.000Z"),
    $lte: new Date("2025-12-31T23:59:59.999Z")
  }
}).pretty()

// 按team_id统计
db.xqbmt_consult.aggregate([
  {
    $match: {
      create_time: {
        $gte: new Date("2025-01-01T00:00:00.000Z"),
        $lte: new Date("2025-12-31T23:59:59.999Z")
      }
    }
  },
  {
    $group: {
      _id: "$team_id",
      count: { $sum: 1 }
    }
  },
  {
    $sort: { count: -1 }
  }
])
```

## ⚙️ 技术实现

### 依赖库
- **xlsx**: Excel文件生成和处理
- **fs**: 文件系统操作
- **path**: 路径处理

### 文件处理
- 临时文件存储在 `exports/` 目录
- 文件发送完成后自动清理（5秒后删除）
- 支持中文文件名的正确编码

### 性能考虑
- 使用MongoDB聚合查询优化性能
- 分批处理大量数据
- 临时文件自动清理避免磁盘空间占用

## 🔒 安全注意事项

- 导出功能不需要身份验证（根据需要可添加）
- 临时文件自动清理，避免敏感数据泄露
- 只导出指定字段，不包含敏感信息
- 文件名包含日期，避免缓存问题

## 📈 使用场景

1. **数据分析**: 分析各团队的咨询数据量和处理情况
2. **报表生成**: 生成月度、季度业务报表
3. **业务监控**: 监控点击记录生成的完成情况
4. **数据备份**: 定期导出数据进行备份
5. **跨部门协作**: 提供Excel格式便于其他部门使用
