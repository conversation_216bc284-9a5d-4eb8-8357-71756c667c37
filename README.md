# Node.js MongoDB 批量数据插入服务

这是一个基于Node.js和MongoDB的批量数据插入服务，可以根据传入的ID数组，为每个ID随机插入300-500条数据。

## 功能特性

- ✅ 连接MongoDB数据库
- ✅ 批量插入数据接口
- ✅ 为每个ID随机生成300-500条数据
- ✅ 数据查询接口
- ✅ 统计数据接口
- ✅ 健康检查接口
- ✅ 错误处理和日志记录

## 环境要求

- Node.js (建议v16+)
- MongoDB (本地或远程)
- npm 或 yarn

## 安装和运行

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
编辑 `.env` 文件，配置MongoDB连接：
```env
MONGODB_URI=mongodb://localhost:27017/batch_data_db
PORT=3000
NODE_ENV=development
```

### 3. 启动MongoDB
确保MongoDB服务正在运行：
```bash
# Windows
net start MongoDB

# macOS/Linux
sudo systemctl start mongod
# 或
brew services start mongodb-community
```

### 4. 启动服务
```bash
npm start
# 或
npm run dev
```

服务将在 `http://localhost:3000` 启动。

## API接口

### 1. 健康检查
```
GET /health
```

响应示例：
```json
{
  "success": true,
  "message": "Node.js服务运行正常",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "mongodb": "已连接"
}
```

### 2. 批量插入数据
```
POST /api/batch-insert
Content-Type: application/json

{
  "ids": ["user001", "user002", "user003"]
}
```

响应示例：
```json
{
  "success": true,
  "message": "批量插入成功",
  "data": {
    "totalInserted": 1247,
    "insertSummary": [
      {"id": "user001", "count": 423},
      {"id": "user002", "count": 387},
      {"id": "user003", "count": 437}
    ],
    "executionTime": "156ms"
  }
}
```

### 3. 查询指定ID的数据
```
GET /api/data/:id
```

响应示例：
```json
{
  "success": true,
  "data": {
    "id": "user001",
    "count": 423,
    "records": [
      {
        "_id": "...",
        "id": "user001",
        "value": 756,
        "timestamp": "2024-01-01T12:00:00.000Z",
        "metadata": {"index": 1, "batch": true}
      }
    ]
  }
}
```

### 4. 获取统计数据
```
GET /api/stats
```

响应示例：
```json
{
  "success": true,
  "data": {
    "totalRecords": 1247,
    "uniqueIds": 3,
    "idStatistics": [
      {
        "_id": "user001",
        "count": 423,
        "latestTimestamp": "2024-01-01T12:00:00.000Z"
      }
    ]
  }
}
```

## 数据模型

每条插入的数据包含以下字段：

```javascript
{
  id: String,        // 传入的ID
  value: Number,     // 1-1000的随机数值
  timestamp: Date,   // 插入时间戳
  metadata: {        // 元数据
    index: Number,   // 在该ID下的索引
    batch: Boolean   // 标识为批量插入
  }
}
```

## 测试

运行测试脚本：
```bash
node test-api.js
```

测试脚本会自动测试所有API接口的功能。

## 项目结构

```
.
├── server.js          # 主服务器文件
├── test-api.js        # API测试脚本
├── package.json       # 项目配置
├── .env              # 环境变量配置
└── README.md         # 项目说明
```

## 注意事项

1. 确保MongoDB服务正在运行
2. 根据需要修改 `.env` 文件中的MongoDB连接字符串
3. 每个ID会随机生成300-500条数据
4. 数据中的value字段是1-1000的随机数
5. 所有插入的数据都会包含时间戳和元数据

## 错误处理

服务包含完整的错误处理机制：
- 输入验证
- MongoDB连接错误处理
- 插入操作错误处理
- 详细的错误日志记录
