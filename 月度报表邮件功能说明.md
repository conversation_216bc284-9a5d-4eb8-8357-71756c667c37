# 📧 月度报表邮件功能说明

## 🎯 功能概述

系统新增了每月自动发送Excel报表到指定QQ邮箱的功能，在每月最后一天的23:30自动执行。

## ⏰ 定时任务配置

### 执行时间
- **定时规则**: `30 23 28-31 * *` (每月28-31日的23:30执行)
- **智能判断**: 只在真正的月末执行（检查明天是否为下月1日）
- **时区设置**: Asia/Shanghai

### 自动启动
- 服务器启动时自动启动月度报表定时任务
- 无需手动干预，开机即用

## 📊 报表内容

### Excel文件结构
1. **2025年咨询数据** - 原始数据详情
   - _id, create_time, consult_title, team_id, open_status, second_count

2. **Team统计** - 按团队分组统计
   - Team名称（中文）、Team ID、总数据条数
   - 有子表记录条数、banner_status=1条数
   - 各种比例统计

3. **月度Team统计** - 按月份和团队交叉统计
   - 每月各团队的数据量、子表记录量、banner状态统计

### 统计条件
- **时间范围**: 2025年全年数据
- **子表条件**: 
  - `reply_user_id ≠ '5f22591c77d7193df40023a2'`
  - `reply_user_id ≠ release_user_id`
- **团队映射**: 显示中文名称（法律帮、汽车帮等）

## 📧 邮件配置

### 环境变量设置
```bash
# 发送方QQ邮箱
EMAIL_USER=<EMAIL>

# QQ邮箱授权码（不是QQ密码）
EMAIL_PASS=your-email-authorization-code

# 接收方邮箱
RECIPIENT_EMAIL=<EMAIL>
```

### QQ邮箱授权码获取步骤
1. 登录QQ邮箱
2. 进入 设置 -> 账户
3. 开启SMTP服务
4. 生成授权码（16位字符）
5. 使用授权码作为EMAIL_PASS

## 🔧 API接口

### 1. 月度报表任务控制
```http
POST /api/monthly-report-control
Content-Type: application/json

{
  "action": "start" | "stop"
}
```

### 2. 手动发送月度报表
```http
POST /api/send-monthly-report
```

### 3. 查看任务状态
```http
GET /api/cron-status
```

响应包含月度报表任务状态：
```json
{
  "success": true,
  "data": {
    "monthlyReportJob": {
      "isRunning": true,
      "schedule": "每月最后一天23:30执行"
    }
  }
}
```

## 📋 邮件内容

### 邮件主题
`{年}年{月}月月度咨询数据报表 - 2025年数据统计`

### 邮件内容
- 📈 数据概览（总数据量、子表记录数、banner状态统计）
- 📋 报表内容说明
- 🏷️ 团队名称映射
- 📎 Excel附件

### 附件文件名
`xqbmt_consult_2025_monthly_report_{日期}.xlsx`

## 🧪 测试功能

### 运行测试
```bash
node test-monthly-report.js
```

### 测试内容
1. 检查月度报表定时任务状态
2. 启动定时任务（如果未启动）
3. 手动发送测试邮件
4. 最终状态检查

## 🔍 日志监控

### 关键日志
```
📅 今天是月末，开始发送月度报表...
📊 开始生成Excel文件...
📋 找到 X 条2025年的咨询数据
📊 子表查询完成，找到 X 条符合条件的子表记录
✅ Excel文件已生成: filename.xlsx
📧 开始发送月度报表邮件...
✅ 邮件发送成功: messageId
🗑️ 临时Excel文件已清理
```

### 错误处理
- Excel生成失败会记录详细错误
- 邮件发送失败会记录错误信息
- 临时文件会在10秒后自动清理

## ⚠️ 注意事项

1. **邮箱配置**: 必须正确配置QQ邮箱和授权码
2. **网络连接**: 确保服务器能访问QQ邮箱服务器
3. **磁盘空间**: Excel文件生成需要临时存储空间
4. **时区设置**: 定时任务使用Asia/Shanghai时区
5. **数据量**: 大量数据可能影响Excel生成速度

## 🚀 部署建议

1. **生产环境**: 使用环境变量配置邮箱信息
2. **监控告警**: 监控邮件发送成功率
3. **备份策略**: 考虑邮件发送失败的重试机制
4. **安全性**: 保护邮箱授权码安全

## 📞 故障排查

### 常见问题
1. **邮件发送失败**: 检查授权码和网络连接
2. **定时任务未执行**: 检查服务器时间和时区
3. **Excel生成失败**: 检查数据库连接和磁盘空间
4. **收不到邮件**: 检查垃圾邮件文件夹

### 调试方法
1. 使用手动发送接口测试
2. 查看服务器日志
3. 检查定时任务状态
4. 验证邮箱配置
