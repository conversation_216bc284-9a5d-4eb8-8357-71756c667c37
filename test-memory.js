const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// 测试批量插入API
async function testBatchInsert() {
  try {
    console.log('🧪 测试批量插入API...');
    
    const testIds = ['user001', 'user002', 'user003', 'user004', 'user005'];
    
    const response = await axios.post(`${BASE_URL}/api/batch-insert`, {
      ids: testIds
    });
    
    console.log('✅ 批量插入测试成功:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 批量插入测试失败:', error.response?.data || error.message);
  }
}

// 测试查询单个ID的数据
async function testQueryData(id) {
  try {
    console.log(`🔍 测试查询ID: ${id} 的数据...`);
    
    const response = await axios.get(`${BASE_URL}/api/data/${id}`);
    
    console.log(`✅ 查询 ${id} 成功，数据量: ${response.data.data.count}`);
    console.log('前3条数据:');
    console.log(JSON.stringify(response.data.data.records.slice(0, 3), null, 2));
    
    return response.data;
  } catch (error) {
    console.error(`❌ 查询 ${id} 失败:`, error.response?.data || error.message);
  }
}

// 测试统计数据API
async function testStats() {
  try {
    console.log('📊 测试统计数据API...');
    
    const response = await axios.get(`${BASE_URL}/api/stats`);
    
    console.log('✅ 统计数据获取成功:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 统计数据获取失败:', error.response?.data || error.message);
  }
}

// 测试健康检查
async function testHealth() {
  try {
    console.log('🏥 测试健康检查...');
    
    const response = await axios.get(`${BASE_URL}/health`);
    
    console.log('✅ 健康检查成功:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 健康检查失败:', error.response?.data || error.message);
  }
}

// 测试清空数据
async function testClear() {
  try {
    console.log('🗑️ 测试清空数据API...');
    
    const response = await axios.delete(`${BASE_URL}/api/clear`);
    
    console.log('✅ 清空数据成功:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 清空数据失败:', error.response?.data || error.message);
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始运行API测试（内存模式）...\n');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 1. 健康检查
  await testHealth();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 2. 清空数据（确保从干净状态开始）
  await testClear();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 3. 批量插入测试
  const insertResult = await testBatchInsert();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 4. 查询数据测试
  if (insertResult && insertResult.success) {
    const firstId = insertResult.data.insertSummary[0].id;
    await testQueryData(firstId);
    console.log('\n' + '='.repeat(50) + '\n');
  }
  
  // 5. 统计数据测试
  await testStats();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 6. 再次批量插入不同的数据
  console.log('🔄 测试第二次批量插入...');
  const secondInsertResult = await axios.post(`${BASE_URL}/api/batch-insert`, {
    ids: ['product001', 'product002']
  });
  console.log('✅ 第二次批量插入成功:');
  console.log(JSON.stringify(secondInsertResult.data, null, 2));
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 7. 最终统计
  await testStats();
  
  console.log('\n🎉 所有测试完成！');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testBatchInsert,
  testQueryData,
  testStats,
  testHealth,
  testClear,
  runAllTests
};
