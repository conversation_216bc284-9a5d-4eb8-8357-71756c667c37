const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// 测试插入数据并检查 __v 字段
async function testVersionKeyRemoval() {
  try {
    console.log('🧪 测试插入数据并检查 __v 字段...');
    
    // 插入测试数据
    const insertResponse = await axios.post(`${BASE_URL}/api/batch-insert`, {
      ids: ['version_test_001']
    });
    
    console.log('✅ 数据插入成功:');
    console.log(`插入数据量: ${insertResponse.data.data.totalInserted}`);
    
    // 查询插入的数据
    const queryResponse = await axios.get(`${BASE_URL}/api/data/version_test_001`);
    
    console.log('\n🔍 查询插入的数据:');
    const records = queryResponse.data.data.records;
    
    if (records && records.length > 0) {
      const firstRecord = records[0];
      console.log('第一条记录的字段:');
      console.log(Object.keys(firstRecord));
      
      // 检查是否包含 __v 字段
      const hasVersionKey = firstRecord.hasOwnProperty('__v');
      
      console.log('\n📊 __v 字段检查结果:');
      console.log(`包含 __v 字段: ${hasVersionKey}`);
      
      if (hasVersionKey) {
        console.log(`⚠️  __v 字段值: ${firstRecord.__v}`);
        console.log('💡 说明: __v 字段仍然存在，这是 Mongoose 的版本控制字段');
      } else {
        console.log('✅ __v 字段已成功移除');
      }
      
      // 显示完整的记录结构
      console.log('\n📋 完整记录结构:');
      console.log(JSON.stringify(firstRecord, null, 2));
      
      return { hasVersionKey, record: firstRecord };
    } else {
      console.log('❌ 没有找到插入的数据');
      return null;
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    return null;
  }
}

// 比较有无 versionKey 的差异
async function compareWithAndWithoutVersionKey() {
  console.log('📊 __v 字段说明和影响分析:\n');
  
  console.log('🔍 什么是 __v 字段:');
  console.log('- 名称: Version Key（版本键）');
  console.log('- 类型: Number');
  console.log('- 默认值: 0');
  console.log('- 作用: 用于乐观锁定（Optimistic Locking）\n');
  
  console.log('🎯 主要作用:');
  console.log('1. 并发控制: 防止多个操作同时修改同一文档时的冲突');
  console.log('2. 数据一致性: 确保文档在修改过程中没有被其他操作更改');
  console.log('3. 版本追踪: 每次文档更新时，__v 值会自动递增\n');
  
  console.log('📈 对应用的影响:');
  console.log('正面影响:');
  console.log('  ✅ 提供数据安全性');
  console.log('  ✅ 防止并发修改冲突');
  console.log('  ✅ 符合 MongoDB 最佳实践\n');
  
  console.log('可能的影响:');
  console.log('  📦 增加少量存储空间（每个文档 +4 字节）');
  console.log('  🔄 如果与其他系统集成，可能需要处理这个额外字段\n');
  
  console.log('⚙️  配置选项:');
  console.log('- 保留 __v 字段: 不添加任何配置（默认行为）');
  console.log('- 移除 __v 字段: 在 Schema 选项中添加 { versionKey: false }');
  console.log('- 自定义字段名: 在 Schema 选项中添加 { versionKey: "version" }\n');
}

// 运行测试
async function runTest() {
  console.log('🚀 开始 __v 字段测试...\n');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 显示说明信息
  await compareWithAndWithoutVersionKey();
  
  console.log('='.repeat(60) + '\n');
  
  // 测试当前配置
  const result = await testVersionKeyRemoval();
  
  console.log('\n' + '='.repeat(60) + '\n');
  
  if (result) {
    console.log('📝 测试结论:');
    if (result.hasVersionKey) {
      console.log('🔧 当前配置: 保留 __v 字段');
      console.log('💡 如需移除，请在 Schema 中添加 { versionKey: false }');
    } else {
      console.log('🔧 当前配置: 已移除 __v 字段');
      console.log('✅ 数据结构更简洁，适合与外部系统集成');
    }
  }
  
  console.log('\n🎉 __v 字段测试完成！');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = {
  testVersionKeyRemoval,
  compareWithAndWithoutVersionKey,
  runTest
};
