# 📧 Outlook 邮箱配置修复指南

## 🚨 当前问题
错误：`connect ECONNREFUSED 127.0.0.1:587`

这个错误表明邮件客户端无法连接到 SMTP 服务器。

## 🔧 解决方案

### 方案1：修正 Outlook Exchange 配置
```javascript
const EMAIL_CONFIG = {
  host: 'smtp.partner.outlook.cn',
  port: 587,
  secure: false, // 使用 STARTTLS
  auth: {
    user: '<EMAIL>',
    pass: '54321abcde?'
  },
  tls: {
    rejectUnauthorized: false,
    ciphers: 'SSLv3'
  },
  connectionTimeout: 60000,
  greetingTimeout: 30000,
  socketTimeout: 60000
};
```

### 方案2：尝试 SSL 端口
```javascript
const EMAIL_CONFIG = {
  host: 'smtp.partner.outlook.cn',
  port: 465,
  secure: true, // 使用 SSL
  auth: {
    user: '<EMAIL>',
    pass: '54321abcde?'
  },
  tls: {
    rejectUnauthorized: false
  }
};
```

### 方案3：使用标准 Outlook 配置
```javascript
const EMAIL_CONFIG = {
  service: 'Outlook365',
  auth: {
    user: '<EMAIL>',
    pass: '54321abcde?'
  },
  tls: {
    rejectUnauthorized: false
  }
};
```

## 🔍 诊断步骤

### 1. 测试网络连接
```bash
# 测试是否能连接到邮件服务器
telnet smtp.partner.outlook.cn 587
# 或
telnet smtp.partner.outlook.cn 465
```

### 2. 检查防火墙设置
确保以下端口未被阻止：
- 587 (STARTTLS)
- 465 (SSL)
- 993 (IMAP SSL)

### 3. 验证邮箱设置
- 确认邮箱地址正确
- 确认密码正确
- 检查是否需要应用专用密码

## 🏢 企业邮箱特殊配置

### Exchange Server 配置
如果是企业内部 Exchange 服务器：
```javascript
const EMAIL_CONFIG = {
  host: 'your-exchange-server.company.com',
  port: 587,
  secure: false,
  auth: {
    user: 'username', // 可能只需要用户名，不需要完整邮箱
    pass: 'password'
  },
  tls: {
    rejectUnauthorized: false
  }
};
```

### Office 365 配置
如果使用 Office 365：
```javascript
const EMAIL_CONFIG = {
  host: 'smtp.office365.com',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: '54321abcde?'
  },
  tls: {
    ciphers: 'SSLv3'
  }
};
```

## 🧪 测试方法

### 1. 使用测试脚本
```bash
node test-email-config.js
```

### 2. 使用 API 接口测试
```bash
# 测试连接
curl -X POST http://localhost:3000/api/test-email-connection

# 发送测试邮件
curl -X POST http://localhost:3000/api/send-test-email
```

### 3. 逐步测试
1. 先测试连接：`/api/test-email-connection`
2. 再测试发送：`/api/send-test-email`
3. 最后测试报表：`/api/send-monthly-report`

## 🔐 安全设置

### 可能需要的邮箱设置
1. **启用 SMTP 认证**
2. **允许安全性较低的应用**（如果适用）
3. **生成应用专用密码**（如果启用了双因素认证）

### 企业环境考虑
1. **代理服务器**：检查是否需要配置代理
2. **网络策略**：确认 SMTP 端口未被阻止
3. **域名解析**：确认能正确解析邮件服务器域名

## 📞 联系 IT 支持

如果以上方法都不行，建议联系公司 IT 支持确认：
1. 正确的 SMTP 服务器地址和端口
2. 是否需要特殊的认证方式
3. 是否有网络限制或代理设置
4. 是否需要在邮箱中启用特定设置

## 🔄 备选方案

### 使用其他邮箱服务
如果企业邮箱配置复杂，可以考虑：
1. **QQ邮箱**：配置简单，支持授权码
2. **163邮箱**：国内访问稳定
3. **Gmail**：需要应用专用密码

### QQ邮箱配置示例
```javascript
const EMAIL_CONFIG = {
  service: 'qq',
  auth: {
    user: '<EMAIL>',
    pass: 'your-authorization-code' // 16位授权码
  }
};
```

## 📝 下一步操作

1. 尝试上述配置方案
2. 运行测试脚本验证
3. 查看详细错误日志
4. 如需要，联系 IT 支持获取正确配置
