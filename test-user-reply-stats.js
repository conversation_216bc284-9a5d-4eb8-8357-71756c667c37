const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testUserReplyStats() {
  console.log('📊 开始测试userData用户回复统计功能...\n');

  try {
    // 1. 获取用户回复统计数据
    console.log('1️⃣ 获取用户回复统计数据...');
    const statsResponse = await axios.get(`${BASE_URL}/api/user-reply-stats-2025`);
    console.log('📊 统计数据概览:', {
      totalReplies: statsResponse.data.data.summary.totalReplies,
      activeUsers: statsResponse.data.data.summary.activeUsers,
      totalUsers: statsResponse.data.data.summary.totalUsers,
      months: statsResponse.data.data.summary.months
    });
    console.log('');

    // 2. 显示前5名活跃用户
    console.log('2️⃣ 前5名活跃用户:');
    const topUsers = statsResponse.data.data.userStats.slice(0, 5);
    topUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.userName} (${user.userId})`);
      console.log(`      总回复数: ${user.totalReplies} (${user.percentage})`);
      
      // 显示月度数据
      const monthlyData = Object.entries(user.monthlyData)
        .filter(([, count]) => count > 0)
        .map(([month, count]) => `${month}: ${count}`)
        .join(', ');
      if (monthlyData) {
        console.log(`      月度分布: ${monthlyData}`);
      }
      console.log('');
    });

    // 3. 显示月度汇总
    console.log('3️⃣ 月度汇总统计:');
    statsResponse.data.data.monthlySummary.forEach(month => {
      console.log(`   ${month.month}: ${month.totalReplies} 条回复 (${month.percentage})`);
    });
    console.log('');

    // 4. 显示用户活跃情况
    console.log('4️⃣ 用户活跃情况:');
    const activeUsers = statsResponse.data.data.userInfo.filter(user => user.isActive);
    const inactiveUsers = statsResponse.data.data.userInfo.filter(user => !user.isActive);
    console.log(`   活跃用户: ${activeUsers.length} 人`);
    console.log(`   非活跃用户: ${inactiveUsers.length} 人`);
    console.log(`   活跃率: ${((activeUsers.length / statsResponse.data.data.summary.totalUsers) * 100).toFixed(2)}%`);
    console.log('');

    // 5. 询问是否下载Excel文件
    console.log('5️⃣ 询问是否下载Excel文件...');
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise((resolve) => {
      rl.question('是否下载Excel文件？(y/n): ', (answer) => {
        rl.close();
        resolve(answer.toLowerCase());
      });
    });

    if (answer === 'y' || answer === 'yes') {
      console.log('📥 开始下载Excel文件...');
      try {
        const excelResponse = await axios.get(`${BASE_URL}/api/export-user-reply-stats-2025`, {
          responseType: 'arraybuffer'
        });
        
        const fs = require('fs');
        const fileName = `user_reply_stats_2025_${new Date().toISOString().split('T')[0]}.xlsx`;
        fs.writeFileSync(fileName, excelResponse.data);
        
        console.log(`✅ Excel文件下载成功: ${fileName}`);
        console.log(`📊 文件大小: ${(excelResponse.data.length / 1024).toFixed(2)} KB`);
      } catch (error) {
        console.error('❌ Excel文件下载失败:', error.response?.data || error.message);
      }
    } else {
      console.log('⏭️  跳过Excel文件下载');
    }

    console.log('\n✅ 用户回复统计功能测试完成！');
    
    // 显示功能说明
    showUserReplyStatsGuide();

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

function showUserReplyStatsGuide() {
  console.log('\n📋 用户回复统计功能说明:');
  console.log('');
  
  console.log('🎯 功能概述:');
  console.log('- 统计userData中用户在xqbmt_consult_second表中的回复数量');
  console.log('- 按2025年月份区分统计');
  console.log('- 排除reply_user_id = release_user_id的自回复情况');
  console.log('- 排除特定系统用户(5f22591c77d7193df40023a2)的回复');
  console.log('');
  
  console.log('📊 统计维度:');
  console.log('- 用户维度: 每个用户的总回复数和月度分布');
  console.log('- 月度维度: 每月的总回复数和占比');
  console.log('- 活跃度: 区分活跃用户和非活跃用户');
  console.log('');
  
  console.log('📱 API接口:');
  console.log('- GET /api/user-reply-stats-2025 - 获取统计数据(JSON)');
  console.log('- GET /api/export-user-reply-stats-2025 - 导出Excel文件');
  console.log('');
  
  console.log('📋 Excel文件结构:');
  console.log('- 工作表1: 用户月度回复统计');
  console.log('- 工作表2: 月度汇总统计');
  console.log('- 工作表3: 用户基础信息');
  console.log('');
  
  console.log('🔍 查询条件:');
  console.log('- 时间范围: 2025-01-01 至 2025-12-31');
  console.log('- reply_user_id ≠ 5f22591c77d7193df40023a2');
  console.log('- reply_user_id ≠ release_user_id');
  console.log('- reply_user_id 必须在userData中存在');
  console.log('');
  
  console.log('📈 数据价值:');
  console.log('- 了解各用户的回复活跃度');
  console.log('- 分析月度回复趋势');
  console.log('- 识别核心活跃用户');
  console.log('- 为用户管理提供数据支持');
}

async function demonstrateUsage() {
  console.log('💡 用户回复统计使用示例:\n');
  
  console.log('1. 获取统计数据:');
  console.log('   curl http://localhost:3000/api/user-reply-stats-2025');
  console.log('');
  
  console.log('2. 下载Excel文件:');
  console.log('   curl -O -J http://localhost:3000/api/export-user-reply-stats-2025');
  console.log('');
  
  console.log('3. 浏览器访问:');
  console.log('   http://localhost:3000/api/user-reply-stats-2025');
  console.log('   http://localhost:3000/api/export-user-reply-stats-2025');
  console.log('');
}

// 显示userData用户列表
function showUserDataInfo() {
  console.log('👥 userData用户信息:\n');
  
  console.log('📊 用户总数: 共包含多个用户');
  console.log('🏷️  用户类型: 律师、记者、管理员等');
  console.log('🔍 统计范围: 只统计userData中存在的用户');
  console.log('');
  
  console.log('📋 部分用户示例:');
  console.log('- 小强热线帮忙团管理员');
  console.log('- 吴迪律师');
  console.log('- 蒋宙烨');
  console.log('- 葛凤杰');
  console.log('- 汪英来老师');
  console.log('- 徐超勤记者');
  console.log('- 等等...');
  console.log('');
}

// 主函数
async function main() {
  console.log('🎯 userData用户回复统计测试工具\n');
  
  // 显示用户信息
  showUserDataInfo();
  
  // 显示使用示例
  await demonstrateUsage();
  
  // 运行测试
  await testUserReplyStats();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testUserReplyStats,
  showUserReplyStatsGuide,
  demonstrateUsage,
  showUserDataInfo
};
