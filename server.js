const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
const pwd = encodeURIComponent('JK20210301xqbmt!');
// MongoDB连接
mongoose.connect('mongodb://xqbmt:'+ pwd +'@dds-bp1e5148281f71a42706-pub.mongodb.rds.aliyuncs.com:3717/jykj', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('✅ MongoDB连接成功');
})
.catch((error) => {
  console.error('❌ MongoDB连接失败:', error);
  console.log('💡 提示: 请确保MongoDB服务正在运行');
  console.log('💡 或者可以使用Docker启动MongoDB: docker run -d -p 27017:27017 mongo');
});

// 数据模型
const DataSchema = new mongoose.Schema({
  consult_id: {
    type: String,
    default: "5f22591c77d7193df40023a2"
  },
  user_id: {
    type: String,
    default: "5f22591c77d7193df40023a2"
  },
  create_time: {
    type: Date,
    default: Date.now
  },
  update_time: {
    type: Date,
    default: Date.now
  },
  del_flag: {
    type:  Number,
    default: 0
  },
  source: {
    type:  Number,
    default: 0
  },
  second_id: {
    type: String,
    required: false
  }
}, {
  versionKey: false  // 禁用 __v 字段
});

const DataModel = mongoose.model('xqbmt_clicks_record', DataSchema, 'xqbmt_clicks_record');
// 生成随机数据的函数
function generateRandomData(id, count) {
  const data = [];
  for (let i = 0; i < count; i++) {
    data.push({
      consult_id: id,
      user_id: "5f22591c77d7193df40023a2", // 1-1000的随机数
      create_time: new Date(),
      del_flag: 0,
      second_id: null,
      source: 0,
      update_time: new Date()
    });
  }
  return data;
}

// 检查集合是否存在的函数
async function checkCollectionExists(collectionName) {
  try {
    const collections = await mongoose.connection.db.listCollections({ name: collectionName }).toArray();
    return collections.length > 0;
  } catch (error) {
    console.error('❌ 检查集合存在性失败:', error);
    return false;
  }
}

// 批量插入数据的接口
app.post('/api/batch-insert', async (req, res) => {
  try {
    const { ids, collection_name } = req.body;

    // 验证输入
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'ids参数必须是非空数组'
      });
    }

    // 如果指定了集合名称，检查集合是否存在
    const targetCollectionName = collection_name || 'xqbmt_clicks_record';
    console.log(`🔍 检查集合是否存在: ${targetCollectionName}`);

    const collectionExists = await checkCollectionExists(targetCollectionName);

    if (!collectionExists) {
      console.log(`❌ 集合不存在: ${targetCollectionName}`);
      return res.status(400).json({
        success: false,
        message: `表单不存在: ${targetCollectionName}`,
        error: 'COLLECTION_NOT_EXISTS'
      });
    }

    console.log(`✅ 集合存在: ${targetCollectionName}`);
    console.log(`📝 开始批量插入数据，ID数量: ${ids.length}`);

    const allData = [];
    const insertSummary = [];

    // 为每个ID生成随机数量的数据
    for (const id of ids) {
      const randomCount = Math.floor(Math.random() * 201) + 300; // 300-500之间的随机数
      const dataForId = generateRandomData(id, randomCount);
      allData.push(...dataForId);

      insertSummary.push({
        id: id,
        count: randomCount
      });

      console.log(`📊 ID: ${id} - 生成 ${randomCount} 条数据`);
    }

    // 如果指定了不同的集合名称，创建对应的模型
    let TargetModel = DataModel;
    if (targetCollectionName !== 'xqbmt_clicks_record') {
      // 第三个参数指定确切的集合名称，防止Mongoose自动复数化
      TargetModel = mongoose.model(targetCollectionName + '_model', DataSchema, targetCollectionName);
    }

    // 批量插入到MongoDB
    const startTime = Date.now();
    const result = await TargetModel.insertMany(allData);
    const endTime = Date.now();

    console.log(`✅ 批量插入完成，总计 ${result.length} 条数据，耗时 ${endTime - startTime}ms`);

    res.json({
      success: true,
      message: '批量插入成功',
      data: {
        collection: targetCollectionName,
        totalInserted: result.length,
        insertSummary: insertSummary,
        executionTime: `${endTime - startTime}ms`
      }
    });

  } catch (error) {
    console.error('❌ 批量插入失败:', error);
    res.status(500).json({
      success: false,
      message: '批量插入失败',
      error: error.message
    });
  }
});

// 查询数据的接口（用于验证插入结果）
app.get('/api/data/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const data = await DataModel.find({ id: id }).sort({ timestamp: -1 });
    
    res.json({
      success: true,
      data: {
        id: id,
        count: data.length,
        records: data
      }
    });
  } catch (error) {
    console.error('❌ 查询数据失败:', error);
    res.status(500).json({
      success: false,
      message: '查询数据失败',
      error: error.message
    });
  }
});

// 获取数据统计的接口
app.get('/api/stats', async (req, res) => {
  try {
    const totalCount = await DataModel.countDocuments();
    const uniqueIds = await DataModel.distinct('id');
    
    const idStats = await DataModel.aggregate([
      {
        $group: {
          _id: '$id',
          count: { $sum: 1 },
          latestTimestamp: { $max: '$timestamp' }
        }
      },
      {
        $sort: { latestTimestamp: -1 }
      }
    ]);

    res.json({
      success: true,
      data: {
        totalRecords: totalCount,
        uniqueIds: uniqueIds.length,
        idStatistics: idStats
      }
    });
  } catch (error) {
    console.error('❌ 获取统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

// 列出所有集合的接口
app.get('/api/collections', async (req, res) => {
  try {
    const collections = await mongoose.connection.db.listCollections().toArray();
    const collectionNames = collections.map(col => col.name);

    res.json({
      success: true,
      message: '获取集合列表成功',
      data: {
        collections: collectionNames,
        count: collectionNames.length
      }
    });
  } catch (error) {
    console.error('❌ 获取集合列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取集合列表失败',
      error: error.message
    });
  }
});

// 创建集合的接口
app.post('/api/create-collection', async (req, res) => {
  try {
    const { collection_name } = req.body;

    if (!collection_name) {
      return res.status(400).json({
        success: false,
        message: 'collection_name参数是必需的'
      });
    }

    // 检查集合是否已存在
    const exists = await checkCollectionExists(collection_name);
    if (exists) {
      return res.json({
        success: true,
        message: `集合已存在: ${collection_name}`,
        data: {
          collection: collection_name,
          action: 'already_exists'
        }
      });
    }

    // 创建集合（通过创建一个临时文档然后删除）
    const TempModel = mongoose.model(collection_name + '_temp', DataSchema, collection_name);
    const tempDoc = new TempModel({
      consult_id: 'temp',
      user_id: 'temp'
    });
    await tempDoc.save();
    await TempModel.deleteOne({ _id: tempDoc._id });

    console.log(`✅ 集合创建成功: ${collection_name}`);

    res.json({
      success: true,
      message: `集合创建成功: ${collection_name}`,
      data: {
        collection: collection_name,
        action: 'created'
      }
    });

  } catch (error) {
    console.error('❌ 创建集合失败:', error);
    res.status(500).json({
      success: false,
      message: '创建集合失败',
      error: error.message
    });
  }
});

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Node.js服务运行正常',
    timestamp: new Date().toISOString(),
    mongodb: mongoose.connection.readyState === 1 ? '已连接' : '未连接'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器启动成功，端口: ${PORT}`);
  console.log(`📍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📍 批量插入接口: POST http://localhost:${PORT}/api/batch-insert`);
  console.log(`📍 查询数据接口: GET http://localhost:${PORT}/api/data/:id`);
  console.log(`📍 统计数据接口: GET http://localhost:${PORT}/api/stats`);
  console.log(`📍 集合列表接口: GET http://localhost:${PORT}/api/collections`);
  console.log(`📍 创建集合接口: POST http://localhost:${PORT}/api/create-collection`);
});

module.exports = app;
