const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const cron = require('node-cron');
const axios = require('axios');
const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');
const nodemailer = require('nodemailer');

const app = express();
const PORT = 3051;


const userData = [
{mongo_user_id:"5f280ab1380628f2ada88b04",publish_name:"小强热线帮忙团管理员"}
,{mongo_user_id:"5f2b615e380628f2adb291c3",publish_name:"吴迪律师"}
,{mongo_user_id:"5f334313380628f2adbe43c8",publish_name:"蒋宙烨"}
,{mongo_user_id:"5f334473380628f2adbe486d",publish_name:"葛凤杰"}
,{mongo_user_id:"5f3344d0380628f2adbe4a65",publish_name:"汪英来老师"}
,{mongo_user_id:"5f334541380628f2adbe4b70",publish_name:"徐超勤记者"}
,{mongo_user_id:"5f335d5a380628f2adbe8a61",publish_name:"潘跃明"}
,{mongo_user_id:"5f5987a6380628f2ad723484",publish_name:"陈可可"}
,{mongo_user_id:"5f61b223380628f2ad81ba50",publish_name:"戚歆如律师"}
,{mongo_user_id:"5f61b2ed380628f2ad81bc51",publish_name:"卫泽玮律师"}
,{mongo_user_id:"5f61b2f6380628f2ad81bc71",publish_name:"白晓强律师"}
,{mongo_user_id:"5f61b31c380628f2ad81bd08",publish_name:"陈煌锦律师"}
,{mongo_user_id:"5f61b38f380628f2ad81be2f",publish_name:"尹瑞华律师"}
,{mongo_user_id:"5f61b3b1380628f2ad81beff",publish_name:"徐洁律师"}
,{mongo_user_id:"5f61b3ff380628f2ad81c077",publish_name:"宋嘉麒律师"}
,{mongo_user_id:"5f61b472380628f2ad81c1de",publish_name:"何丽君律师"}
,{mongo_user_id:"5f61b498380628f2ad81c245",publish_name:"袁世健律师"}
,{mongo_user_id:"5f61b5ac380628f2ad81c511",publish_name:"郭立成律师"}
,{mongo_user_id:"5f61b604380628f2ad81c5ae",publish_name:"钟浩谦律师"}
,{mongo_user_id:"5f61b653380628f2ad81c63c",publish_name:"詹奇超律师"}
,{mongo_user_id:"5f61b75b380628f2ad81c842",publish_name:"应鑫垚律师"}
,{mongo_user_id:"5f61b9f2380628f2ad81cf64",publish_name:"孙佳波律师"}
,{mongo_user_id:"5f61b9f8380628f2ad81cf7d",publish_name:"吴燕律师"}
,{mongo_user_id:"5f61ba0e380628f2ad81d084",publish_name:"泮美丹律师"}
,{mongo_user_id:"5f61ba51380628f2ad81d207",publish_name:"陈婷婷律师"}
,{mongo_user_id:"5f61ba84380628f2ad81d372",publish_name:"徐丽萍律师"}
,{mongo_user_id:"5f61bb0c380628f2ad81d6f5",publish_name:"朱祉江律师"}
,{mongo_user_id:"5f61bc04380628f2ad81da0c",publish_name:"杨甜律师"}
,{mongo_user_id:"5f61bc09380628f2ad81da26",publish_name:"汪芳颖律师"}
,{mongo_user_id:"5f61bd2f380628f2ad81dc1b",publish_name:"季列夫律师"}
,{mongo_user_id:"5f61bf58380628f2ad81e01f",publish_name:"谭焦律师"}
,{mongo_user_id:"5f61bfe4380628f2ad81e1a3",publish_name:"夏洋律师"}
,{mongo_user_id:"5f61c153380628f2ad81e492",publish_name:"张玉律师"}
,{mongo_user_id:"5f61cc35380628f2ad81faeb",publish_name:"邢琼律师"}
,{mongo_user_id:"5f61d2ff380628f2ad8208b8",publish_name:"蔡勤为律师"}
,{mongo_user_id:"5f61ddc0380628f2ad8217e2",publish_name:"施巧巧律师"}
,{mongo_user_id:"5f630cda380628f2ad83b16a",publish_name:"史莉佳律师"}
,{mongo_user_id:"5f630cf8380628f2ad83b1cc",publish_name:"王岩律师"}
,{mongo_user_id:"5f630cff380628f2ad83b1ea",publish_name:"王敏律师"}
,{mongo_user_id:"5f630d9f380628f2ad83b395",publish_name:"巴菱律师"}
,{mongo_user_id:"5f631499380628f2ad83bfbd",publish_name:"何丽青律师"}
,{mongo_user_id:"5f681dad380628f2ad8ad2dc",publish_name:"朱楚君律师"}
,{mongo_user_id:"5f72c55f380628f2ad9e1296",publish_name:"王路易记者"}
,{mongo_user_id:"5f757782380628f2ada270ad",publish_name:"韩仁国记者"}
,{mongo_user_id:"5f926e7d380628f2adca2868",publish_name:"蔡钿记者"}
,{mongo_user_id:"5fb7464d8b6c567323c33890",publish_name:"何佳妮律师"}
,{mongo_user_id:"5fbcb1a48b6c567323cac40f",publish_name:"俞天玮"}
,{mongo_user_id:"5fbde00a8b6c567323ccadf5",publish_name:"吴施臻记者"}
,{mongo_user_id:"5fbde3308b6c567323ccd5fa",publish_name:"张钰记者"}
,{mongo_user_id:"600585198b6c567323428bd9",publish_name:"俞潇尧记者"}
,{mongo_user_id:"6008e69e8b6c5673234e08ec",publish_name:"方美玉医师"}
,{mongo_user_id:"6010dc7b8b6c567323663547",publish_name:"王辉炜记者"}
,{mongo_user_id:"601121d58b6c567323671788",publish_name:"吴老师"}
,{mongo_user_id:"601125338b6c567323672327",publish_name:"郭老师"}
,{mongo_user_id:"601254c48b6c5673236ad1d7",publish_name:"孟冠英律师"}
,{mongo_user_id:"601254e58b6c5673236ad42a",publish_name:"武四化律师"}
,{mongo_user_id:"6013b5b68b6c567323706077",publish_name:"孙吉莹律师"}
,{mongo_user_id:"6038539d8b6c567323c97e84",publish_name:"孙老师"}
,{mongo_user_id:"60385b818b6c567323c9ab76",publish_name:"资深教育媒体人夏沫"}
,{mongo_user_id:"604ec4a08b6c56732305a53e",publish_name:"丁斌浩记者"}
,{mongo_user_id:"604ecc0f8b6c56732305e958",publish_name:"徐凤记者"}
,{mongo_user_id:"60d93db421f36a3de92cb55a",publish_name:"张亚西律师"}
,{mongo_user_id:"615710eb21f36a3de965e035",publish_name:"小强热线帮忙团管理员"}
,{mongo_user_id:"61a8445521f36a3de9743e0a",publish_name:"江海涛专家"}
,{mongo_user_id:"61a86f3421f36a3de975d7b3",publish_name:"丁金旺医师"}
,{mongo_user_id:"61a870b021f36a3de975e7c6",publish_name:"吴婉莉医师"}
,{mongo_user_id:"61a9a0ea21f36a3de98186ed",publish_name:"毛晓春医师"}
,{mongo_user_id:"61af620f21f36a3de9b95d5d",publish_name:"刘兰英医师"}
,{mongo_user_id:"61b86ae921f36a3de913d16b",publish_name:"邹小冬医师"}
,{mongo_user_id:"61b8713b21f36a3de9140ece",publish_name:"张震中医师"}
,{mongo_user_id:"61b8970021f36a3de915832a",publish_name:"许先荣医师"}
,{mongo_user_id:"61b8a05c21f36a3de915e66d",publish_name:"孙迪医师"}
,{mongo_user_id:"61b8a0e921f36a3de915f4c8",publish_name:"韩知忖医师"}
,{mongo_user_id:"61b8a2c121f36a3de9160df7",publish_name:"陈伟军医师"}
,{mongo_user_id:"61b8a67421f36a3de916366a",publish_name:"蒋慧芳医师"}
,{mongo_user_id:"61b93d5e21f36a3de91bd648",publish_name:"徐俊峰医师"}
,{mongo_user_id:"61b9865421f36a3de91ebb5e",publish_name:"王晓丽医师"}
,{mongo_user_id:"61dcfa4a21f36a3de9958f1f",publish_name:"王文栋医师"}
,{mongo_user_id:"61dfee8821f36a3de9b5e30e",publish_name:"龙则灵律师"}
,{mongo_user_id:"61e4c9e121f36a3de9e94e16",publish_name:"朱卫东老师"}
,{mongo_user_id:"61e660a521f36a3de9fb67d4",publish_name:"张鸣九律师"}
,{mongo_user_id:"61ef897821f36a3de95b0162",publish_name:"矫金玲专家"}
,{mongo_user_id:"61f3a28e21f36a3de9848442",publish_name:"黄诗舟记者"}
,{mongo_user_id:"61f3a61b21f36a3de984b28f",publish_name:"郑飞翔记者"}
,{mongo_user_id:"61f7823021f36a3de9aadd72",publish_name:"李富钊记者"}
,{mongo_user_id:"621ee4b021f36a3de93b7768",publish_name:"蔡琛律师"}
,{mongo_user_id:"6243d85521f36a3de9bc4103",publish_name:"郭晨阳律师"}
,{mongo_user_id:"62752b3621f36a3de9d20c88",publish_name:"胡波记者"}
,{mongo_user_id:"6361df4021f36a3de9bb6272",publish_name:"方昱婷律师"}
,{mongo_user_id:"6361fcfc21f36a3de9bc9903",publish_name:"毛敏杰律师"}
,{mongo_user_id:"63e30c9921f36a3de99772d0",publish_name:"耿铭"}
,{mongo_user_id:"660cb89b21f36a3de9a7d5ff",publish_name:"陈婷律师"}
,{mongo_user_id:"660cbbd121f36a3de9a7f730",publish_name:"丁景荣律师"}
,{mongo_user_id:"660d056321f36a3de9aaf9b2",publish_name:"吴秋叶律师"}
,{mongo_user_id:"660d05d021f36a3de9ab004a",publish_name:"劳子桢律师"}
,{mongo_user_id:"660d05d621f36a3de9ab00d8",publish_name:"李宇平律师"}]
// 中间件
app.use(cors());
app.use(express.json());
const pwd = encodeURIComponent('JK20210301xqbmt!');
// MongoDB连接
mongoose.connect('mongodb://xqbmt:'+ pwd +'@dds-bp1e5148281f71a42706-pub.mongodb.rds.aliyuncs.com:3717/jykj', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('✅ MongoDB连接成功');
})
.catch((error) => {
  console.error('❌ MongoDB连接失败:', error);
  console.log('💡 提示: 请确保MongoDB服务正在运行');
  console.log('💡 或者可以使用Docker启动MongoDB: docker run -d -p 27017:27017 mongo');
});
// const pwd = encodeURIComponent('zmj123456a?');
// // MongoDB连接
// mongoose.connect('mongodb://zmj:'+ pwd +'@metaverse.fujisoft-china.com:7799/zmj', {
//   useNewUrlParser: true,
//   useUnifiedTopology: true,
// })
// .then(() => {
//   console.log('✅ MongoDB连接成功');
// })
// .catch((error) => {
//   console.error('❌ MongoDB连接失败:', error);
//   console.log('💡 提示: 请确保MongoDB服务正在运行');
//   console.log('💡 或者可以使用Docker启动MongoDB: docker run -d -p 27017:27017 mongo');
// });

// 数据模型
const DataSchema = new mongoose.Schema({
  consult_id: {
    type: String,
    default: "5f22591c77d7193df40023a2"
  },
  user_id: {
    type: String,
    default: "5f22591c77d7193df40023a2"
  },
  create_time: {
    type: Date,
    default: Date.now
  },
  update_time: {
    type: Date,
    default: Date.now
  },
  del_flag: {
    type:  Number,
    default: 0
  },
  source: {
    type:  Number,
    default: 0
  },
  second_id: {
    type: String,
    required: false
  }
}, {
  versionKey: false  // 禁用 __v 字段
});

const DataModel = mongoose.model('xqbmt_clicks_record', DataSchema, 'xqbmt_clicks_record');

// xqbmt_consult 集合的模型（用于定时任务查询）
const ConsultSchema = new mongoose.Schema({
  _id: mongoose.Schema.Types.ObjectId,
  open_status: {
    type: Number,
    required: true
  },
  banner_status: {
    type: Number,
    required: false
  },
  // 添加其他可能需要的字段
  create_time: Date,
  update_time: Date,
  clicks_generated: {
    type: Date,
    required: false  // 点击记录生成时间标记，有值表示已生成过
  }
}, {
  versionKey: false,
  strict: false  // 允许其他字段存在
});

const ConsultModel = mongoose.model('xqbmt_consult', ConsultSchema, 'xqbmt_consult');

// xqbmt_consult_second 子表模型
const ConsultSecondSchema = new mongoose.Schema({
  _id: mongoose.Schema.Types.ObjectId,
  consult_id: {
    type: String,
    required: true
  },
  reply_user_id: {
    type: String,
    required: true
  },
  release_user_id: {
    type: String,
    required: false
  },
  create_time: Date,
  update_time: Date
}, {
  versionKey: false,
  strict: false
});

const ConsultSecondModel = mongoose.model('xqbmt_consult_second', ConsultSecondSchema, 'xqbmt_consult_second');

// team_id 中文名称映射
const TEAM_NAME_MAP = {
  '5f0416f1e2bfa86f8c797c36': '法律帮',
  '5f04170ae2bfa86f8c797c37': '汽车帮',
  '5f3ba44077d7190bf80077c9': '新闻爆料',
  '5f041726e2bfa86f8c797c38': '房产帮',
  '60fa1e5b77d7191870003102': '电话报料',
  '617f7d6e9cb5820bdac71f37': '教育帮'  // 注意：您提到的两个ID相同，我假设教育帮是不同的ID
};

// 获取team的中文名称
function getTeamDisplayName(teamId) {
  if (!teamId) return '未分组';
  return TEAM_NAME_MAP[teamId] || teamId;
}

// 邮件配置
const EMAIL_CONFIG = {
  host: 'smtp.partner.outlook.cn',
  port: 587,
  secure: false, // 587端口使用STARTTLS，不是SSL
  auth: {
    user: '<EMAIL>',
    pass: '54321abcde?'
  },
  tls: {
    ciphers: 'SSLv3',
    rejectUnauthorized: false
  },
  pool: true,
  maxConnections: 5,
  maxMessages: 10
};

// 收件人配置 - 支持多个邮箱，用逗号分隔
const RECIPIENTS = ('<EMAIL>,<EMAIL>')
  .split(',')
  .map(email => email.trim())
  .filter(email => email.length > 0);

// 创建邮件传输器
const transporter = nodemailer.createTransport(EMAIL_CONFIG);

// 生成Excel文件并返回文件路径
async function generateExcelFile() {
  try {
    console.log('📊 开始生成Excel文件...');

    // 设置2025年的时间范围
    const startDate = new Date('2025-01-01T00:00:00.000Z');
    const endDate = new Date('2025-12-31T23:59:59.999Z');

    // 查询2025年的所有数据
    const consultData = await ConsultModel.find({
      create_time: {
        $gte: startDate,
        $lte: endDate
      }
    }).select('_id create_time consult_title team_id open_status banner_status').sort({ create_time: 1 });

    console.log(`📋 找到 ${consultData.length} 条2025年的咨询数据`);

    // 查询子表数据并统计
    const consultIds = consultData.map(item => item._id.toString());
    const secondData = await ConsultSecondModel.find({
      consult_id: { $in: consultIds },
      reply_user_id: { $ne: '5f22591c77d7193df40023a2' },
      $expr: { $ne: ['$reply_user_id', '$release_user_id'] }
    }).select('consult_id reply_user_id release_user_id');

    // 统计每个consult_id的子表记录数
    const secondCountMap = {};
    secondData.forEach(item => {
      if (!secondCountMap[item.consult_id]) {
        secondCountMap[item.consult_id] = 0;
      }
      secondCountMap[item.consult_id]++;
    });

    console.log(`📊 子表查询完成，找到 ${secondData.length} 条符合条件的子表记录`);

    // 按team_id统计数据
    const teamStats = {};
    let totalCount = 0;

    consultData.forEach(item => {
      const teamId = item.team_id || '未分组';
      const teamDisplayName = getTeamDisplayName(teamId);
      if (!teamStats[teamDisplayName]) {
        teamStats[teamDisplayName] = {
          total: 0,
          withSecond: 0,
          bannerStatus1: 0,
          originalTeamId: teamId
        };
      }
      teamStats[teamDisplayName].total++;
      totalCount++;

      const secondCount = secondCountMap[item._id.toString()] || 0;
      if (secondCount > 0) {
        teamStats[teamDisplayName].withSecond++;
      }

      if (item.banner_status === 1) {
        teamStats[teamDisplayName].bannerStatus1++;
      }
    });

    // 创建Excel工作簿
    const workbook = XLSX.utils.book_new();

    // 工作表1: 原始数据
    const rawData = consultData.map(item => {
      const secondCount = secondCountMap[item._id.toString()] || 0;
      return {
        '_id': item._id.toString(),
        'create_time': item.create_time ? item.create_time.toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '') : '',
        'consult_title': item.consult_title || '',
        'team_id': item.team_id || '',
        'open_status': item.open_status,
        'second_count': secondCount
      };
    });

    const rawDataSheet = XLSX.utils.json_to_sheet(rawData);
    XLSX.utils.book_append_sheet(workbook, rawDataSheet, '2025年咨询数据');

    // 工作表2: 按team_id统计
    const statsData = Object.entries(teamStats).map(([teamDisplayName, stats]) => ({
      'Team名称': teamDisplayName,
      'Team ID': stats.originalTeamId,
      '总数据条数': stats.total,
      '记者回复条数': stats.withSecond,
      '推送至问答条数': stats.bannerStatus1,
      '记者回复率': stats.total > 0 ? `${((stats.withSecond / stats.total) * 100).toFixed(2)}%` : '0%',
      '推送至问答率': stats.total > 0 ? `${((stats.bannerStatus1 / stats.total) * 100).toFixed(2)}%` : '0%',
      '占比': `${((stats.total / totalCount) * 100).toFixed(2)}%`
    }));

    const totalWithSecond = Object.values(teamStats).reduce((sum, stats) => sum + stats.withSecond, 0);
    const totalBannerStatus1 = Object.values(teamStats).reduce((sum, stats) => sum + stats.bannerStatus1, 0);
    statsData.push({
      'Team名称': '总计',
      'Team ID': '',
      '总数据条数': totalCount,
      '记者回复条数': totalWithSecond,
      '推送至问答条数': totalBannerStatus1,
      '记者回复率': totalCount > 0 ? `${((totalWithSecond / totalCount) * 100).toFixed(2)}%` : '0%',
      '推送至问答率': totalCount > 0 ? `${((totalBannerStatus1 / totalCount) * 100).toFixed(2)}%` : '0%',
      '占比': '100.00%'
    });

    const statsSheet = XLSX.utils.json_to_sheet(statsData);
    XLSX.utils.book_append_sheet(workbook, statsSheet, 'Team统计');

    // 工作表3: 月度统计
    const monthlyTeamStats = {};
    consultData.forEach(item => {
      if (item.create_time) {
        const month = item.create_time.toISOString().substring(0, 7);
        const teamId = item.team_id || '未分组';
        const teamDisplayName = getTeamDisplayName(teamId);
        const secondCount = secondCountMap[item._id.toString()] || 0;

        if (!monthlyTeamStats[month]) {
          monthlyTeamStats[month] = {};
        }
        if (!monthlyTeamStats[month][teamDisplayName]) {
          monthlyTeamStats[month][teamDisplayName] = {
            total: 0,
            withSecond: 0,
            bannerStatus1: 0
          };
        }
        monthlyTeamStats[month][teamDisplayName].total++;
        if (secondCount > 0) {
          monthlyTeamStats[month][teamDisplayName].withSecond++;
        }
        if (item.banner_status === 1) {
          monthlyTeamStats[month][teamDisplayName].bannerStatus1++;
        }
      }
    });

    const allTeamDisplayNames = [...new Set(consultData.map(item => getTeamDisplayName(item.team_id)))].sort();

    const monthlyData = [];
    Object.entries(monthlyTeamStats)
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([month, teams]) => {
        const row = { '月份': month };
        let monthTotal = 0;
        let monthWithSecond = 0;
        let monthBannerStatus1 = 0;

        allTeamDisplayNames.forEach(teamDisplayName => {
          const stats = teams[teamDisplayName] || { total: 0, withSecond: 0, bannerStatus1: 0 };
          row[`${teamDisplayName}_报料总数`] = stats.total;
          row[`${teamDisplayName}_记者回复`] = stats.withSecond;
          row[`${teamDisplayName}_推送问答`] = stats.bannerStatus1;
          monthTotal += stats.total;
          monthWithSecond += stats.withSecond;
          monthBannerStatus1 += stats.bannerStatus1;
        });

        row['月度报料总计'] = monthTotal;
        row['月度记者回复总计'] = monthWithSecond;
        row['月度推送问答总计'] = monthBannerStatus1;
        monthlyData.push(row);
      });

    const totalRow = { '月份': '总计' };
    let grandTotal = 0;
    let grandWithSecond = 0;
    let grandBannerStatus1 = 0;
    allTeamDisplayNames.forEach(teamDisplayName => {
      const teamData = consultData.filter(item => getTeamDisplayName(item.team_id) === teamDisplayName);
      const teamTotal = teamData.length;
      const teamWithSecondTotal = teamData.filter(item => (secondCountMap[item._id.toString()] || 0) > 0).length;
      const teamBannerStatus1Total = teamData.filter(item => item.banner_status === 1).length;

      totalRow[`${teamDisplayName}_报料总数`] = teamTotal;
      totalRow[`${teamDisplayName}_记者回复`] = teamWithSecondTotal;
      totalRow[`${teamDisplayName}_推送问答`] = teamBannerStatus1Total;
      grandTotal += teamTotal;
      grandWithSecond += teamWithSecondTotal;
      grandBannerStatus1 += teamBannerStatus1Total;
    });
    totalRow['月度报料总计'] = grandTotal;
    totalRow['月度记者回复总计'] = grandWithSecond;
    totalRow['月度推送问答总计'] = grandBannerStatus1;
    monthlyData.push(totalRow);

    const monthlySheet = XLSX.utils.json_to_sheet(monthlyData);
    XLSX.utils.book_append_sheet(workbook, monthlySheet, '月度Team统计');

    // 生成文件名
    const fileName = `xqbmt_consult_2025_monthly_report_${new Date().toISOString().split('T')[0]}.xlsx`;
    const filePath = path.join(__dirname, 'reports', fileName);

    // 确保reports目录存在
    const reportsDir = path.join(__dirname, 'reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    // 写入文件
    XLSX.writeFile(workbook, filePath);

    console.log(`✅ Excel文件已生成: ${fileName}`);

    return {
      filePath: filePath,
      fileName: fileName,
      totalCount: totalCount,
      withSecondCount: totalWithSecond,
      bannerStatus1Count: totalBannerStatus1
    };

  } catch (error) {
    console.error('❌ 生成Excel文件失败:', error);
    throw error;
  }
}

// 发送邮件函数
async function sendMonthlyReport() {
  try {
    console.log('📧 开始发送月度报表邮件...');

    // 生成Excel文件
    const excelResult = await generateExcelFile();

    // 获取当前月份信息
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const monthName = `${year}年${month}月`;

    // 邮件内容
    const mailOptions = {
      from: EMAIL_CONFIG.auth.user,
      to: RECIPIENTS.join(','),
      subject: `${monthName}月度咨询数据报表 - 2025年数据统计`,
      html: `
        <h2>📊 ${monthName}月度咨询数据报表</h2>
        <p>您好！</p>
        <p>这是系统自动生成的月度咨询数据报表，包含2025年全年数据的详细统计。</p>

        <h3>📈 数据概览</h3>
        <ul>
          <li><strong>总数据量：</strong>${excelResult.totalCount} 条</li>
          <li><strong>记者回复：</strong>${excelResult.withSecondCount} 条</li>
          <li><strong>推送问答：</strong>${excelResult.bannerStatus1Count} 条</li>
        </ul>

        <h3>📋 报表内容</h3>
        <p>Excel文件包含以下工作表：</p>
        <ul>
          <li><strong>2025年咨询数据：</strong>原始数据详情</li>
          <li><strong>Team统计：</strong>按团队分组的统计信息</li>
          <li><strong>月度Team统计：</strong>按月份和团队的交叉统计</li>
        </ul>

        <h3>🏷️ 团队名称说明</h3>
        <ul>
          <li>法律帮、汽车帮、新闻爆料、房产帮、电话报料、教育帮</li>
        </ul>

        <p>如有任何问题，请联系系统管理员。</p>
        <p><em>此邮件由系统自动发送，请勿回复。</em></p>

        <hr>
        <p style="color: #666; font-size: 12px;">
          发送时间：${now.toLocaleString('zh-CN')}<br>
          系统：咨询数据统计系统
        </p>
      `,
      attachments: [
        {
          filename: excelResult.fileName,
          path: excelResult.filePath
        }
      ]
    };

    // 发送邮件
    const info = await transporter.sendMail(mailOptions);
    console.log('✅ 邮件发送成功:', info.messageId);

    // 删除临时文件
    setTimeout(() => {
      try {
        fs.unlinkSync(excelResult.filePath);
        console.log('🗑️  临时Excel文件已清理');
      } catch (deleteErr) {
        console.error('⚠️  清理临时文件失败:', deleteErr);
      }
    }, 10000); // 10秒后删除

    return {
      success: true,
      messageId: info.messageId,
      fileName: excelResult.fileName,
      recipients: RECIPIENTS
    };

  } catch (error) {
    console.error('❌ 发送月度报表邮件失败:', error);
    throw error;
  }
}

// 存储上次检查的时间戳
let lastCheckTime = new Date();

// 定时任务：检查新的 open_status=1 且未生成点击记录的数据
async function checkNewConsultData() {
  try {
    console.log(`🕐 [${new Date().toISOString()}] 开始检查需要生成点击记录的咨询数据...`);

    // 设置日期限制：只检查2025-06-01 00:00:00之后的数据
    const dateLimit = new Date('2025-06-01T00:00:00.000Z');

    // 查询 open_status=1 且 clicks_generated 字段不存在或为空，且 create_time >= 2025-06-01 的数据
    const newConsults = await ConsultModel.find({
      open_status: 1,
      create_time: { $gte: dateLimit },  // 只检查2025-06-01之后的数据
      $or: [
        { clicks_generated: { $exists: false } },  // 字段不存在
        { clicks_generated: null },                // 字段为 null
        { clicks_generated: { $eq: "" } }          // 字段为空字符串
      ]
    }).select('_id create_time update_time clicks_generated');

    console.log(`📅 日期限制: 只检查 ${dateLimit.toISOString()} 之后创建的数据`);

    if (newConsults.length > 0) {
      console.log(`🔍 发现 ${newConsults.length} 条需要生成点击记录的咨询数据`);

      // 提取 _id 作为批量插入的 ids
      const ids = newConsults.map(consult => consult._id.toString());
      const consultIds = newConsults.map(consult => consult._id);

      console.log(`📋 准备批量插入的 IDs: ${ids.join(', ')}`);

      // 显示这些记录的创建时间
      newConsults.forEach((consult, index) => {
        console.log(`   ${index + 1}. ID: ${consult._id}, 创建时间: ${consult.create_time}`);
      });

      try {
        // 调用批量插入接口
        const insertResult = await callBatchInsertAPI(ids);

        if (insertResult && insertResult.success) {
          // 批量插入成功后，更新这些记录的 clicks_generated 字段
          const updateResult = await ConsultModel.updateMany(
            { _id: { $in: consultIds } },
            {
              $set: {
                clicks_generated: new Date(),
                clicks_count: insertResult.data.totalInserted
              }
            }
          );

          console.log(`✅ 已标记 ${updateResult.modifiedCount} 条记录为已生成点击记录`);
          console.log(`📊 总共生成 ${insertResult.data.totalInserted} 条点击记录`);
        }

      } catch (insertError) {
        console.error('❌ 批量插入失败，不更新标记字段:', insertError.message);
      }

    } else {
      console.log(`ℹ️  没有发现需要生成点击记录的咨询数据 (2025-06-01之后且未处理)`);
    }

    // 更新最后检查时间
    lastCheckTime = new Date();
    console.log(`✅ 定时任务完成，检查时间: ${lastCheckTime.toISOString()}`);

  } catch (error) {
    console.error('❌ 定时任务执行失败:', error);
  }
}

// 调用批量插入 API
async function callBatchInsertAPI(ids) {
  try {
    const response = await axios.post(`http://localhost:${PORT}/api/batch-insert`, {
      ids: ids,
      collection_name: 'xqbmt_clicks_record'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000  // 30秒超时
    });

    console.log(`✅ 批量插入成功: ${response.data.data.totalInserted} 条数据`);
    console.log(`📊 插入详情: ${JSON.stringify(response.data.data.insertSummary)}`);

    return response.data;
  } catch (error) {
    console.error('❌ 调用批量插入API失败:', error.response?.data || error.message);
    throw error;
  }
}
// 生成随机数据的函数
function generateRandomData(id, count) {
  const data = [];
  for (let i = 0; i < count; i++) {
    data.push({
      consult_id: id,
      user_id: "5f22591c77d7193df40023a2", // 1-1000的随机数
      create_time: new Date(),
      del_flag: 0,
      second_id: null,
      source: 0,
      update_time: new Date()
    });
  }
  return data;
}

// 检查集合是否存在的函数
async function checkCollectionExists(collectionName) {
  try {
    const collections = await mongoose.connection.db.listCollections({ name: collectionName }).toArray();
    return collections.length > 0;
  } catch (error) {
    console.error('❌ 检查集合存在性失败:', error);
    return false;
  }
}

// 批量插入数据的接口
app.post('/api/batch-insert', async (req, res) => {
  try {
    const { ids, collection_name } = req.body;

    // 验证输入
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'ids参数必须是非空数组'
      });
    }

    // 如果指定了集合名称，检查集合是否存在
    const targetCollectionName = collection_name || 'xqbmt_clicks_record';
    console.log(`🔍 检查集合是否存在: ${targetCollectionName}`);

    const collectionExists = await checkCollectionExists(targetCollectionName);

    if (!collectionExists) {
      console.log(`❌ 集合不存在: ${targetCollectionName}`);
      return res.status(400).json({
        success: false,
        message: `表单不存在: ${targetCollectionName}`,
        error: 'COLLECTION_NOT_EXISTS'
      });
    }

    console.log(`✅ 集合存在: ${targetCollectionName}`);
    console.log(`📝 开始批量插入数据，ID数量: ${ids.length}`);

    const allData = [];
    const insertSummary = [];

    // 为每个ID生成随机数量的数据
    for (const id of ids) {
      const randomCount = Math.floor(Math.random() * 201) + 300; // 300-500之间的随机数
      const dataForId = generateRandomData(id, randomCount);
      allData.push(...dataForId);

      insertSummary.push({
        id: id,
        count: randomCount
      });

      console.log(`📊 ID: ${id} - 生成 ${randomCount} 条数据`);
    }

    // 如果指定了不同的集合名称，创建对应的模型
    let TargetModel = DataModel;
    if (targetCollectionName !== 'xqbmt_clicks_record') {
      // 第三个参数指定确切的集合名称，防止Mongoose自动复数化
      TargetModel = mongoose.model(targetCollectionName + '_model', DataSchema, targetCollectionName);
    }

    // 批量插入到MongoDB
    const startTime = Date.now();
    const result = await TargetModel.insertMany(allData);
    const endTime = Date.now();

    console.log(`✅ 批量插入完成，总计 ${result.length} 条数据，耗时 ${endTime - startTime}ms`);

    res.json({
      success: true,
      message: '批量插入成功',
      data: {
        collection: targetCollectionName,
        totalInserted: result.length,
        insertSummary: insertSummary,
        executionTime: `${endTime - startTime}ms`
      }
    });

  } catch (error) {
    console.error('❌ 批量插入失败:', error);
    res.status(500).json({
      success: false,
      message: '批量插入失败',
      error: error.message
    });
  }
});

// 查询数据的接口（用于验证插入结果）
app.get('/api/data/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const data = await DataModel.find({ id: id }).sort({ timestamp: -1 });
    
    res.json({
      success: true,
      data: {
        id: id,
        count: data.length,
        records: data
      }
    });
  } catch (error) {
    console.error('❌ 查询数据失败:', error);
    res.status(500).json({
      success: false,
      message: '查询数据失败',
      error: error.message
    });
  }
});

// 获取数据统计的接口
app.get('/api/stats', async (req, res) => {
  try {
    const totalCount = await DataModel.countDocuments();
    const uniqueIds = await DataModel.distinct('id');
    
    const idStats = await DataModel.aggregate([
      {
        $group: {
          _id: '$id',
          count: { $sum: 1 },
          latestTimestamp: { $max: '$timestamp' }
        }
      },
      {
        $sort: { latestTimestamp: -1 }
      }
    ]);

    res.json({
      success: true,
      data: {
        totalRecords: totalCount,
        uniqueIds: uniqueIds.length,
        idStatistics: idStats
      }
    });
  } catch (error) {
    console.error('❌ 获取统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

// 列出所有集合的接口
app.get('/api/collections', async (req, res) => {
  try {
    const collections = await mongoose.connection.db.listCollections().toArray();
    const collectionNames = collections.map(col => col.name);

    res.json({
      success: true,
      message: '获取集合列表成功',
      data: {
        collections: collectionNames,
        count: collectionNames.length
      }
    });
  } catch (error) {
    console.error('❌ 获取集合列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取集合列表失败',
      error: error.message
    });
  }
});

// 创建集合的接口
app.post('/api/create-collection', async (req, res) => {
  try {
    const { collection_name } = req.body;

    if (!collection_name) {
      return res.status(400).json({
        success: false,
        message: 'collection_name参数是必需的'
      });
    }

    // 检查集合是否已存在
    const exists = await checkCollectionExists(collection_name);
    if (exists) {
      return res.json({
        success: true,
        message: `集合已存在: ${collection_name}`,
        data: {
          collection: collection_name,
          action: 'already_exists'
        }
      });
    }

    // 创建集合（通过创建一个临时文档然后删除）
    const TempModel = mongoose.model(collection_name + '_temp', DataSchema, collection_name);
    const tempDoc = new TempModel({
      consult_id: 'temp',
      user_id: 'temp'
    });
    await tempDoc.save();
    await TempModel.deleteOne({ _id: tempDoc._id });

    console.log(`✅ 集合创建成功: ${collection_name}`);

    res.json({
      success: true,
      message: `集合创建成功: ${collection_name}`,
      data: {
        collection: collection_name,
        action: 'created'
      }
    });

  } catch (error) {
    console.error('❌ 创建集合失败:', error);
    res.status(500).json({
      success: false,
      message: '创建集合失败',
      error: error.message
    });
  }
});

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Node.js服务运行正常',
    timestamp: new Date().toISOString(),
    mongodb: mongoose.connection.readyState === 1 ? '已连接' : '未连接'
  });
});

// 定时任务状态管理
let cronJob = null;
let monthlyReportJob = null;
let isTaskRunning = false;

// 启动定时任务
function startCronJob() {
  if (cronJob) {
    console.log('⚠️  定时任务已在运行中');
    return false;
  }

  // 每10分钟执行一次：'*/10 * * * *'
  cronJob = cron.schedule('*/10 * * * *', async () => {
    if (isTaskRunning) {
      console.log('⚠️  上一个定时任务仍在执行中，跳过本次执行');
      return;
    }

    isTaskRunning = true;
    try {
      await checkNewConsultData();
    } finally {
      isTaskRunning = false;
    }
  }, {
    scheduled: false  // 不立即启动
  });

  cronJob.start();
  console.log('✅ 定时任务已启动 (每10分钟执行一次)');
  return true;
}

// 停止定时任务
function stopCronJob() {
  if (cronJob) {
    cronJob.stop();
    cronJob = null;
    console.log('🛑 定时任务已停止');
    return true;
  }
  console.log('⚠️  定时任务未在运行');
  return false;
}

// 启动月度报表定时任务
function startMonthlyReportJob() {
  if (monthlyReportJob) {
    console.log('⚠️  月度报表定时任务已在运行中');
    return false;
  }

  try {
    // 每月28号上午10:00执行
    monthlyReportJob = cron.schedule('0 14 28 * *', async () => {
      console.log('📅 每月28号定时发送月度报表...');
      try {
        await sendMonthlyReport();
        console.log('✅ 月度报表发送成功');
      } catch (error) {
        console.error('❌ 月度报表发送失败:', error);
      }
    }, {
      scheduled: false,
      timezone: "Asia/Shanghai"
    });

    monthlyReportJob.start();
    console.log('✅ 月度报表定时任务启动成功');
    return true;
  } catch (error) {
    console.error('❌ 启动月度报表定时任务失败:', error);
    return false;
  }
}

// 停止月度报表定时任务
function stopMonthlyReportJob() {
  if (monthlyReportJob) {
    monthlyReportJob.stop();
    monthlyReportJob = null;
    console.log('🛑 月度报表定时任务已停止');
    return true;
  }
  console.log('⚠️  月度报表定时任务未在运行');
  return false;
}

// 手动触发定时任务
app.post('/api/trigger-check', async (req, res) => {
  try {
    if (isTaskRunning) {
      return res.status(409).json({
        success: false,
        message: '定时任务正在执行中，请稍后再试'
      });
    }

    console.log('🔧 手动触发定时任务...');
    isTaskRunning = true;

    await checkNewConsultData();

    res.json({
      success: true,
      message: '手动检查完成',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 手动触发失败:', error);
    res.status(500).json({
      success: false,
      message: '手动触发失败',
      error: error.message
    });
  } finally {
    isTaskRunning = false;
  }
});

// 定时任务状态查询
app.get('/api/cron-status', (req, res) => {
  res.json({
    success: true,
    data: {
      isRunning: cronJob !== null,
      isTaskExecuting: isTaskRunning,
      lastCheckTime: lastCheckTime.toISOString(),
      schedule: '每10分钟执行一次',
      monthlyReportJob: {
        isRunning: monthlyReportJob !== null,
        schedule: '每月28号下午14:00执行',
        recipients: RECIPIENTS
      }
    }
  });
});

// 启动/停止定时任务
app.post('/api/cron-control', (req, res) => {
  const { action } = req.body;

  let result = false;
  let message = '';

  switch (action) {
    case 'start':
      result = startCronJob();
      message = result ? '定时任务启动成功' : '定时任务已在运行中';
      break;
    case 'stop':
      result = stopCronJob();
      message = result ? '定时任务停止成功' : '定时任务未在运行';
      break;
    default:
      return res.status(400).json({
        success: false,
        message: 'action 参数必须是 start 或 stop'
      });
  }

  res.json({
    success: true,
    message: message,
    data: {
      action: action,
      result: result,
      isRunning: cronJob !== null
    }
  });
});

// 月度报表定时任务控制
app.post('/api/monthly-report-control', (req, res) => {
  const { action } = req.body;

  let result = false;
  let message = '';

  switch (action) {
    case 'start':
      result = startMonthlyReportJob();
      message = result ? '月度报表定时任务启动成功' : '月度报表定时任务已在运行中';
      break;
    case 'stop':
      result = stopMonthlyReportJob();
      message = result ? '月度报表定时任务停止成功' : '月度报表定时任务未在运行';
      break;
    default:
      return res.status(400).json({
        success: false,
        message: 'action 参数必须是 start 或 stop'
      });
  }

  res.json({
    success: true,
    message: message,
    data: {
      action: action,
      result: result,
      isRunning: monthlyReportJob !== null
    }
  });
});

// 手动发送月度报表
app.post('/api/send-monthly-report', async (req, res) => {
  try {
    console.log('📧 手动触发月度报表发送...');

    const result = await sendMonthlyReport();

    res.json({
      success: true,
      message: '月度报表发送成功',
      data: result
    });

  } catch (error) {
    console.error('❌ 手动发送月度报表失败:', error);
    res.status(500).json({
      success: false,
      message: '月度报表发送失败',
      error: error.message
    });
  }
});

// 测试邮件连接
app.post('/api/test-email-connection', async (req, res) => {
  try {
    console.log('🔍 测试邮件服务器连接...');

    // 验证邮件传输器配置
    await transporter.verify();
    console.log('✅ 邮件服务器连接成功');

    res.json({
      success: true,
      message: '邮件服务器连接成功',
      config: {
        host: EMAIL_CONFIG.host,
        port: EMAIL_CONFIG.port,
        secure: EMAIL_CONFIG.secure,
        user: EMAIL_CONFIG.auth.user
      }
    });

  } catch (error) {
    console.error('❌ 邮件服务器连接失败:', error);
    res.status(500).json({
      success: false,
      message: '邮件服务器连接失败',
      error: error.message,
      config: {
        host: EMAIL_CONFIG.host,
        port: EMAIL_CONFIG.port,
        secure: EMAIL_CONFIG.secure,
        user: EMAIL_CONFIG.auth.user
      }
    });
  }
});

// 发送测试邮件
app.post('/api/send-test-email', async (req, res) => {
  try {
    console.log('📧 发送测试邮件...');

    const mailOptions = {
      from: EMAIL_CONFIG.auth.user,
      to: '<EMAIL>',
      subject: '📧 邮件服务测试',
      html: `
        <h2>📧 邮件服务测试</h2>
        <p>这是一封测试邮件，用于验证邮件服务配置是否正确。</p>
        <p><strong>发送时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
        <p><strong>发送方：</strong>${EMAIL_CONFIG.auth.user}</p>
        <p><strong>收件人：</strong>${RECIPIENTS.join(', ')}</p>
        <p><strong>服务器：</strong>${EMAIL_CONFIG.host}:${EMAIL_CONFIG.port}</p>
        <p>如果您收到这封邮件，说明邮件服务配置正确！</p>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('✅ 测试邮件发送成功:', info.messageId);

    res.json({
      success: true,
      message: '测试邮件发送成功',
      data: {
        messageId: info.messageId,
        recipients: RECIPIENTS
      }
    });

  } catch (error) {
    console.error('❌ 测试邮件发送失败:', error);
    res.status(500).json({
      success: false,
      message: '测试邮件发送失败',
      error: error.message
    });
  }
});

// 获取收件人列表
app.get('/api/recipients', (req, res) => {
  res.json({
    success: true,
    data: {
      recipients: RECIPIENTS,
      count: RECIPIENTS.length,
      source: 'RECIPIENT_EMAILS环境变量'
    }
  });
});

// 验证邮箱地址格式
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 临时添加收件人（重启后失效）
app.post('/api/recipients/add', (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: '邮箱地址不能为空'
    });
  }

  if (!isValidEmail(email)) {
    return res.status(400).json({
      success: false,
      message: '邮箱地址格式不正确'
    });
  }

  if (RECIPIENTS.includes(email)) {
    return res.status(400).json({
      success: false,
      message: '邮箱地址已存在'
    });
  }

  RECIPIENTS.push(email);

  res.json({
    success: true,
    message: '收件人添加成功（重启后失效）',
    data: {
      recipients: RECIPIENTS,
      count: RECIPIENTS.length
    }
  });
});

// 临时移除收件人（重启后失效）
app.post('/api/recipients/remove', (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: '邮箱地址不能为空'
    });
  }

  const index = RECIPIENTS.indexOf(email);
  if (index === -1) {
    return res.status(400).json({
      success: false,
      message: '邮箱地址不存在'
    });
  }

  RECIPIENTS.splice(index, 1);

  res.json({
    success: true,
    message: '收件人移除成功（重启后失效）',
    data: {
      recipients: RECIPIENTS,
      count: RECIPIENTS.length
    }
  });
});

// 查询咨询记录的点击生成状态
app.get('/api/consult-status', async (req, res) => {
  try {
    const { page = 1, limit = 20, status } = req.query;
    const skip = (page - 1) * limit;

    // 设置日期限制：只查询2025-06-01 00:00:00之后的数据
    const dateLimit = new Date('2025-06-01T00:00:00.000Z');

    let query = {
      open_status: 1,
      create_time: { $gte: dateLimit }  // 只查询2025-06-01之后的数据
    };

    // 根据状态筛选
    if (status === 'generated') {
      query.clicks_generated = { $exists: true, $ne: null };
    } else if (status === 'pending') {
      query.$or = [
        { clicks_generated: { $exists: false } },
        { clicks_generated: null },
        { clicks_generated: { $eq: "" } }
      ];
    }

    const total = await ConsultModel.countDocuments(query);
    const consults = await ConsultModel.find(query)
      .select('_id open_status clicks_generated clicks_count create_time update_time')
      .sort({ create_time: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    res.json({
      success: true,
      data: {
        consults: consults,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('❌ 查询咨询状态失败:', error);
    res.status(500).json({
      success: false,
      message: '查询咨询状态失败',
      error: error.message
    });
  }
});

// 重置咨询记录的点击生成状态（用于测试）
app.post('/api/reset-consult-status', async (req, res) => {
  try {
    const { consult_ids } = req.body;

    if (!consult_ids || !Array.isArray(consult_ids)) {
      return res.status(400).json({
        success: false,
        message: 'consult_ids 参数必须是数组'
      });
    }

    const result = await ConsultModel.updateMany(
      { _id: { $in: consult_ids } },
      {
        $unset: {
          clicks_generated: "",
          clicks_count: ""
        }
      }
    );

    console.log(`🔄 重置了 ${result.modifiedCount} 条咨询记录的点击生成状态`);

    res.json({
      success: true,
      message: `成功重置 ${result.modifiedCount} 条记录的状态`,
      data: {
        modifiedCount: result.modifiedCount
      }
    });

  } catch (error) {
    console.error('❌ 重置咨询状态失败:', error);
    res.status(500).json({
      success: false,
      message: '重置咨询状态失败',
      error: error.message
    });
  }
});

// 导出2025年xqbmt_consult数据到Excel
app.get('/api/export-consult-2025', async (req, res) => {
  try {
    console.log('📊 开始导出2025年咨询数据...');

    // 设置2025年的时间范围
    const startDate = new Date('2025-01-01T00:00:00.000Z');
    const endDate = new Date('2025-12-31T23:59:59.999Z');

    // 查询2025年的所有数据
    const consultData = await ConsultModel.find({
      create_time: {
        $gte: startDate,
        $lte: endDate
      }
    }).select('_id create_time consult_title team_id open_status').sort({ create_time: 1 });

    console.log(`📋 找到 ${consultData.length} 条2025年的咨询数据`);

    // 按team_id统计数据
    const teamStats = {};
    let totalCount = 0;

    consultData.forEach(item => {
      const teamId = item.team_id || '未分组';
      if (!teamStats[teamId]) {
        teamStats[teamId] = 0;
      }
      teamStats[teamId]++;
      totalCount++;
    });

    console.log('📊 按team_id统计结果:', teamStats);
    console.log(`📈 2025年总计: ${totalCount} 条数据`);

    // 创建Excel工作簿
    const workbook = XLSX.utils.book_new();

    // 工作表1: 原始数据
    const rawData = consultData.map(item => ({
      '_id': item._id.toString(),
      'create_time': item.create_time ? item.create_time.toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '') : '',
      'consult_title': item.consult_title || '',
      'team_id': item.team_id || '',
      'open_status': item.open_status
    }));

    const rawDataSheet = XLSX.utils.json_to_sheet(rawData);
    XLSX.utils.book_append_sheet(workbook, rawDataSheet, '2025年咨询数据');

    // 工作表2: 按team_id统计
    const statsData = Object.entries(teamStats).map(([teamId, count]) => ({
      'Team ID': teamId,
      '数据条数': count,
      '占比': `${((count / totalCount) * 100).toFixed(2)}%`
    }));

    // 添加总计行
    statsData.push({
      'Team ID': '总计',
      '数据条数': totalCount,
      '占比': '100.00%'
    });

    const statsSheet = XLSX.utils.json_to_sheet(statsData);
    XLSX.utils.book_append_sheet(workbook, statsSheet, 'Team统计');

    // 工作表3: 月度统计（按team_id区分）
    const monthlyTeamStats = {};
    consultData.forEach(item => {
      if (item.create_time) {
        const month = item.create_time.toISOString().substring(0, 7); // YYYY-MM
        const teamId = item.team_id || '未分组';

        if (!monthlyTeamStats[month]) {
          monthlyTeamStats[month] = {};
        }
        if (!monthlyTeamStats[month][teamId]) {
          monthlyTeamStats[month][teamId] = 0;
        }
        monthlyTeamStats[month][teamId]++;
      }
    });

    // 获取所有team_id
    const allTeamIds = [...new Set(consultData.map(item => item.team_id || '未分组'))].sort();

    // 转换为表格数据
    const monthlyData = [];
    Object.entries(monthlyTeamStats)
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([month, teams]) => {
        const row = { '月份': month };
        let monthTotal = 0;

        allTeamIds.forEach(teamId => {
          const count = teams[teamId] || 0;
          row[teamId] = count;
          monthTotal += count;
        });

        row['月度总计'] = monthTotal;
        monthlyData.push(row);
      });

    // 添加总计行
    const totalRow = { '月份': '总计' };
    let grandTotal = 0;
    allTeamIds.forEach(teamId => {
      const teamTotal = consultData.filter(item => (item.team_id || '未分组') === teamId).length;
      totalRow[teamId] = teamTotal;
      grandTotal += teamTotal;
    });
    totalRow['月度总计'] = grandTotal;
    monthlyData.push(totalRow);

    const monthlySheet = XLSX.utils.json_to_sheet(monthlyData);
    XLSX.utils.book_append_sheet(workbook, monthlySheet, '月度Team统计');

    // 生成Excel文件
    const fileName = `xqbmt_consult_2025_${new Date().toISOString().split('T')[0]}.xlsx`;
    const filePath = path.join(__dirname, 'exports', fileName);

    // 确保exports目录存在
    const exportsDir = path.join(__dirname, 'exports');
    if (!fs.existsSync(exportsDir)) {
      fs.mkdirSync(exportsDir, { recursive: true });
    }

    // 写入文件
    XLSX.writeFile(workbook, filePath);

    console.log(`✅ Excel文件已生成: ${fileName}`);

    // 设置响应头并发送文件
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);

    // 发送文件
    res.sendFile(filePath, (err) => {
      if (err) {
        console.error('❌ 发送文件失败:', err);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            message: '文件发送失败',
            error: err.message
          });
        }
      } else {
        console.log('✅ Excel文件发送成功');
        // 发送完成后删除临时文件
        setTimeout(() => {
          try {
            fs.unlinkSync(filePath);
            console.log('🗑️  临时文件已清理');
          } catch (deleteErr) {
            console.error('⚠️  清理临时文件失败:', deleteErr);
          }
        }, 5000); // 5秒后删除
      }
    });

  } catch (error) {
    console.error('❌ 导出Excel失败:', error);
    res.status(500).json({
      success: false,
      message: '导出Excel失败',
      error: error.message
    });
  }
});

// 获取2025年咨询数据统计信息（不下载文件）
app.get('/api/consult-stats-2025', async (req, res) => {
  try {
    console.log('📊 开始统计2025年咨询数据...');

    // 设置2025年的时间范围
    const startDate = new Date('2025-07-01T00:00:00.000Z');
    const endDate = new Date('2025-12-31T23:59:59.999Z');

    // 查询2025年的所有数据
    const consultData = await ConsultModel.find({
      create_time: {
        $gte: startDate,
        $lte: endDate
      }
    }).select('team_id create_time clicks_generated');

    console.log(`📋 找到 ${consultData.length} 条2025年的咨询数据`);

    // 按team_id统计数据
    const teamStats = {};
    let totalCount = 0;
    let generatedCount = 0; // 已生成点击记录的数量

    consultData.forEach(item => {
      const teamId = item.team_id || '未分组';
      if (!teamStats[teamId]) {
        teamStats[teamId] = {
          total: 0,
          generated: 0
        };
      }
      teamStats[teamId].total++;
      totalCount++;

      if (item.clicks_generated) {
        teamStats[teamId].generated++;
        generatedCount++;
      }
    });

    // 月度统计（按team_id区分）
    const monthlyTeamStats = {};
    consultData.forEach(item => {
      if (item.create_time) {
        const month = item.create_time.toISOString().substring(0, 7); // YYYY-MM
        const teamId = item.team_id || '未分组';

        if (!monthlyTeamStats[month]) {
          monthlyTeamStats[month] = {};
        }
        if (!monthlyTeamStats[month][teamId]) {
          monthlyTeamStats[month][teamId] = 0;
        }
        monthlyTeamStats[month][teamId]++;
      }
    });

    // 转换为数组格式
    const teamStatsArray = Object.entries(teamStats).map(([teamId, stats]) => ({
      teamId: teamId,
      totalCount: stats.total,
      generatedCount: stats.generated,
      percentage: ((stats.total / totalCount) * 100).toFixed(2) + '%',
      generatedPercentage: stats.total > 0 ? ((stats.generated / stats.total) * 100).toFixed(2) + '%' : '0%'
    })).sort((a, b) => b.totalCount - a.totalCount);

    // 获取所有team_id
    const allTeamIds = [...new Set(consultData.map(item => item.team_id || '未分组'))].sort();

    // 转换月度统计为数组格式
    const monthlyStatsArray = Object.entries(monthlyTeamStats)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([month, teams]) => {
        const monthData = { month: month };
        let monthTotal = 0;

        allTeamIds.forEach(teamId => {
          const count = teams[teamId] || 0;
          monthData[teamId] = count;
          monthTotal += count;
        });

        monthData.total = monthTotal;
        return monthData;
      });

    console.log('📊 统计完成');

    res.json({
      success: true,
      data: {
        summary: {
          totalCount: totalCount,
          generatedCount: generatedCount,
          pendingCount: totalCount - generatedCount,
          generatedPercentage: totalCount > 0 ? ((generatedCount / totalCount) * 100).toFixed(2) + '%' : '0%',
          year: '2025',
          dateRange: {
            start: startDate.toISOString().split('T')[0],
            end: endDate.toISOString().split('T')[0]
          }
        },
        teamStats: teamStatsArray,
        monthlyStats: monthlyStatsArray,
        topTeams: teamStatsArray.slice(0, 10) // 前10个team
      }
    });

  } catch (error) {
    console.error('❌ 统计2025年数据失败:', error);
    res.status(500).json({
      success: false,
      message: '统计数据失败',
      error: error.message
    });
  }
});

// 导出2025年xqbmt_consult数据到Excel（包含子表统计）
app.get('/api/export-consult-2025-with-second', async (req, res) => {
  try {
    console.log('📊 开始导出2025年咨询数据（包含子表统计）...');

    // 设置2025年的时间范围
    const startDate = new Date('2025-01-01T00:00:00.000Z');
    const endDate = new Date('2025-12-31T23:59:59.999Z');

    // 查询2025年的所有数据
    const consultData = await ConsultModel.find({
      create_time: {
        $gte: startDate,
        $lte: endDate
      }
    }).select('_id create_time consult_title team_id open_status banner_status').sort({ create_time: 1 });

    console.log(`📋 找到 ${consultData.length} 条2025年的咨询数据`);

    // 查询子表数据并统计
    console.log('🔍 开始查询子表数据...');
    const consultIds = consultData.map(item => item._id.toString());

    // 批量查询子表数据
    const secondData = await ConsultSecondModel.find({
      consult_id: { $in: consultIds },
      reply_user_id: { $ne: '5f22591c77d7193df40023a2' },
      $expr: { $ne: ['$reply_user_id', '$release_user_id'] }  // reply_user_id 不等于 release_user_id
    }).select('consult_id reply_user_id release_user_id');

    // 统计每个consult_id的子表记录数
    const secondCountMap = {};
    secondData.forEach(item => {
      if (!secondCountMap[item.consult_id]) {
        secondCountMap[item.consult_id] = 0;
      }
      secondCountMap[item.consult_id]++;
    });

    console.log(`📊 子表查询完成，找到 ${secondData.length} 条符合条件的子表记录`);

    // 按team_id统计数据
    const teamStats = {};
    let totalCount = 0;

    consultData.forEach(item => {
      const teamId = item.team_id || '未分组';
      const teamDisplayName = getTeamDisplayName(teamId);
      if (!teamStats[teamDisplayName]) {
        teamStats[teamDisplayName] = {
          total: 0,
          withSecond: 0,
          bannerStatus1: 0,
          originalTeamId: teamId
        };
      }
      teamStats[teamDisplayName].total++;
      totalCount++;

      // 检查是否有子表记录
      const secondCount = secondCountMap[item._id.toString()] || 0;
      if (secondCount > 0) {
        teamStats[teamDisplayName].withSecond++;
      }

      // 检查banner_status是否为1
      if (item.banner_status === 1) {
        teamStats[teamDisplayName].bannerStatus1++;
      }
    });

    console.log('📊 按team_id统计结果:', teamStats);
    console.log(`📈 2025年总计: ${totalCount} 条数据`);

    // 创建Excel工作簿
    const workbook = XLSX.utils.book_new();

    // 工作表1: 原始数据（包含子表统计）
    const rawData = consultData.map(item => {
      const secondCount = secondCountMap[item._id.toString()] || 0;
      return {
        '_id': item._id.toString(),
        'create_time': item.create_time ? item.create_time.toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '') : '',
        'consult_title': item.consult_title || '',
        'team_id': item.team_id || '',
        'open_status': item.open_status,
        'second_count': secondCount
      };
    });

    const rawDataSheet = XLSX.utils.json_to_sheet(rawData);
    XLSX.utils.book_append_sheet(workbook, rawDataSheet, '2025年咨询数据');

    // 工作表2: 按team_id统计
    const statsData = Object.entries(teamStats).map(([teamDisplayName, stats]) => ({
      'Team名称': teamDisplayName,
      'Team ID': stats.originalTeamId,
      '总数据条数': stats.total,
      '记者回复条数': stats.withSecond,
      '推送至问答条数=1条数': stats.bannerStatus1,
      '记者回复率': stats.total > 0 ? `${((stats.withSecond / stats.total) * 100).toFixed(2)}%` : '0%',
      '推送至问答率': stats.total > 0 ? `${((stats.bannerStatus1 / stats.total) * 100).toFixed(2)}%` : '0%',
      '占比': `${((stats.total / totalCount) * 100).toFixed(2)}%`
    }));

    // 添加总计行
    const totalWithSecond = Object.values(teamStats).reduce((sum, stats) => sum + stats.withSecond, 0);
    const totalBannerStatus1 = Object.values(teamStats).reduce((sum, stats) => sum + stats.bannerStatus1, 0);
    statsData.push({
      'Team名称': '总计',
      'Team ID': '',
      '总数据条数': totalCount,
      '记者回复条数': totalWithSecond,
      '推送至问答条数': totalBannerStatus1,
      '记者回复率': totalCount > 0 ? `${((totalWithSecond / totalCount) * 100).toFixed(2)}%` : '0%',
      '推送至问答率': totalCount > 0 ? `${((totalBannerStatus1 / totalCount) * 100).toFixed(2)}%` : '0%',
      '占比': '100.00%'
    });

    const statsSheet = XLSX.utils.json_to_sheet(statsData);
    XLSX.utils.book_append_sheet(workbook, statsSheet, 'Team统计');

    // 工作表3: 月度Team统计（包含子表统计和banner_status统计）
    const monthlyTeamStats = {};
    consultData.forEach(item => {
      if (item.create_time) {
        const month = item.create_time.toISOString().substring(0, 7); // YYYY-MM
        const teamId = item.team_id || '未分组';
        const teamDisplayName = getTeamDisplayName(teamId);
        const secondCount = secondCountMap[item._id.toString()] || 0;

        if (!monthlyTeamStats[month]) {
          monthlyTeamStats[month] = {};
        }
        if (!monthlyTeamStats[month][teamDisplayName]) {
          monthlyTeamStats[month][teamDisplayName] = {
            total: 0,
            withSecond: 0,
            bannerStatus1: 0
          };
        }
        monthlyTeamStats[month][teamDisplayName].total++;
        if (secondCount > 0) {
          monthlyTeamStats[month][teamDisplayName].withSecond++;
        }
        if (item.banner_status === 1) {
          monthlyTeamStats[month][teamDisplayName].bannerStatus1++;
        }
      }
    });

    // 获取所有team显示名称
    const allTeamDisplayNames = [...new Set(consultData.map(item => getTeamDisplayName(item.team_id)))].sort();

    // 转换为表格数据
    const monthlyData = [];
    Object.entries(monthlyTeamStats)
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([month, teams]) => {
        const row = { '月份': month };
        let monthTotal = 0;
        let monthWithSecond = 0;
        let monthBannerStatus1 = 0;

        allTeamDisplayNames.forEach(teamDisplayName => {
          const stats = teams[teamDisplayName] || { total: 0, withSecond: 0, bannerStatus1: 0 };
          row[`${teamDisplayName}_报料总数`] = stats.total;
          row[`${teamDisplayName}_记者回复`] = stats.withSecond;
          row[`${teamDisplayName}_推至问答`] = stats.bannerStatus1;
          monthTotal += stats.total;
          monthWithSecond += stats.withSecond;
          monthBannerStatus1 += stats.bannerStatus1;
        });

        row['月度报料总计'] = monthTotal;
        row['月度记者回复总计'] = monthWithSecond;
        row['月度推至问答总计'] = monthBannerStatus1;
        monthlyData.push(row);
      });

    // 添加总计行
    const totalRow = { '月份': '总计' };
    let grandTotal = 0;
    let grandWithSecond = 0;
    let grandBannerStatus1 = 0;
    allTeamDisplayNames.forEach(teamDisplayName => {
      const teamData = consultData.filter(item => getTeamDisplayName(item.team_id) === teamDisplayName);
      const teamTotal = teamData.length;
      const teamWithSecondTotal = teamData.filter(item => (secondCountMap[item._id.toString()] || 0) > 0).length;
      const teamBannerStatus1Total = teamData.filter(item => item.banner_status === 1).length;

      totalRow[`${teamDisplayName}_报料总数`] = teamTotal;
      totalRow[`${teamDisplayName}_记者回复`] = teamWithSecondTotal;
      totalRow[`${teamDisplayName}_推至问答`] = teamBannerStatus1Total;
      grandTotal += teamTotal;
      grandWithSecond += teamWithSecondTotal;
      grandBannerStatus1 += teamBannerStatus1Total;
    });
    totalRow['月度报料总计'] = grandTotal;
    totalRow['月度记者回复总计'] = grandWithSecond;
    totalRow['月度推至问答总计'] = grandBannerStatus1;
    monthlyData.push(totalRow);

    const monthlySheet = XLSX.utils.json_to_sheet(monthlyData);
    XLSX.utils.book_append_sheet(workbook, monthlySheet, '月度Team统计');

    // 生成Excel文件
    const fileName = `xqbmt_consult_2025_with_second_${new Date().toISOString().split('T')[0]}.xlsx`;
    const filePath = path.join(__dirname, 'exports', fileName);

    // 确保exports目录存在
    const exportsDir = path.join(__dirname, 'exports');
    if (!fs.existsSync(exportsDir)) {
      fs.mkdirSync(exportsDir, { recursive: true });
    }

    // 写入文件
    XLSX.writeFile(workbook, filePath);

    console.log(`✅ Excel文件已生成: ${fileName}`);

    // 设置响应头并发送文件
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);

    // 发送文件
    res.sendFile(filePath, (err) => {
      if (err) {
        console.error('❌ 发送文件失败:', err);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            message: '文件发送失败',
            error: err.message
          });
        }
      } else {
        console.log('✅ Excel文件发送成功');
        // 发送完成后删除临时文件
        setTimeout(() => {
          try {
            fs.unlinkSync(filePath);
            console.log('🗑️  临时文件已清理');
          } catch (deleteErr) {
            console.error('⚠️  清理临时文件失败:', deleteErr);
          }
        }, 5000); // 5秒后删除
      }
    });

  } catch (error) {
    console.error('❌ 导出Excel失败:', error);
    res.status(500).json({
      success: false,
      message: '导出Excel失败',
      error: error.message
    });
  }
});

// 获取2025年咨询数据统计信息（包含子表统计）
app.get('/api/consult-stats-2025-with-second', async (req, res) => {
  try {
    console.log('📊 开始统计2025年咨询数据（包含子表统计）...');

    // 设置2025年的时间范围
    const startDate = new Date('2025-01-01T00:00:00.000Z');
    const endDate = new Date('2025-12-31T23:59:59.999Z');

    // 查询2025年的所有数据
    const consultData = await ConsultModel.find({
      create_time: {
        $gte: startDate,
        $lte: endDate
      }
    }).select('_id team_id create_time clicks_generated banner_status');

    console.log(`📋 找到 ${consultData.length} 条2025年的咨询数据`);

    // 查询子表数据
    const consultIds = consultData.map(item => item._id.toString());
    const secondData = await ConsultSecondModel.find({
      consult_id: { $in: consultIds },
      reply_user_id: { $ne: '5f22591c77d7193df40023a2' },
      $expr: { $ne: ['$reply_user_id', '$release_user_id'] }  // reply_user_id 不等于 release_user_id
    }).select('consult_id reply_user_id release_user_id');

    // 统计每个consult_id的子表记录数
    const secondCountMap = {};
    secondData.forEach(item => {
      if (!secondCountMap[item.consult_id]) {
        secondCountMap[item.consult_id] = 0;
      }
      secondCountMap[item.consult_id]++;
    });

    console.log(`📊 子表查询完成，找到 ${secondData.length} 条符合条件的子表记录`);

    // 按team_id统计数据
    const teamStats = {};
    let totalCount = 0;
    let generatedCount = 0;
    let withSecondCount = 0;
    let bannerStatus1Count = 0;

    consultData.forEach(item => {
      const teamId = item.team_id || '未分组';
      const teamDisplayName = getTeamDisplayName(teamId);
      if (!teamStats[teamDisplayName]) {
        teamStats[teamDisplayName] = {
          total: 0,
          generated: 0,
          withSecond: 0,
          bannerStatus1: 0,
          originalTeamId: teamId
        };
      }
      teamStats[teamDisplayName].total++;
      totalCount++;

      if (item.clicks_generated) {
        teamStats[teamDisplayName].generated++;
        generatedCount++;
      }

      const secondCount = secondCountMap[item._id.toString()] || 0;
      if (secondCount > 0) {
        teamStats[teamDisplayName].withSecond++;
        withSecondCount++;
      }

      if (item.banner_status === 1) {
        teamStats[teamDisplayName].bannerStatus1++;
        bannerStatus1Count++;
      }
    });

    // 月度统计（按team_id区分，包含子表统计和banner_status统计）
    const monthlyTeamStats = {};
    consultData.forEach(item => {
      if (item.create_time) {
        const month = item.create_time.toISOString().substring(0, 7);
        const teamId = item.team_id || '未分组';
        const teamDisplayName = getTeamDisplayName(teamId);
        const secondCount = secondCountMap[item._id.toString()] || 0;

        if (!monthlyTeamStats[month]) {
          monthlyTeamStats[month] = {};
        }
        if (!monthlyTeamStats[month][teamDisplayName]) {
          monthlyTeamStats[month][teamDisplayName] = {
            total: 0,
            withSecond: 0,
            bannerStatus1: 0
          };
        }
        monthlyTeamStats[month][teamDisplayName].total++;
        if (secondCount > 0) {
          monthlyTeamStats[month][teamDisplayName].withSecond++;
        }
        if (item.banner_status === 1) {
          monthlyTeamStats[month][teamDisplayName].bannerStatus1++;
        }
      }
    });

    // 转换为数组格式
    const teamStatsArray = Object.entries(teamStats).map(([teamDisplayName, stats]) => ({
      teamId: stats.originalTeamId,
      teamName: teamDisplayName,
      totalCount: stats.total,
      generatedCount: stats.generated,
      withSecondCount: stats.withSecond,
      bannerStatus1Count: stats.bannerStatus1,
      percentage: ((stats.total / totalCount) * 100).toFixed(2) + '%',
      generatedPercentage: stats.total > 0 ? ((stats.generated / stats.total) * 100).toFixed(2) + '%' : '0%',
      withSecondPercentage: stats.total > 0 ? ((stats.withSecond / stats.total) * 100).toFixed(2) + '%' : '0%',
      bannerStatus1Percentage: stats.total > 0 ? ((stats.bannerStatus1 / stats.total) * 100).toFixed(2) + '%' : '0%'
    })).sort((a, b) => b.totalCount - a.totalCount);

    // 获取所有team显示名称
    const allTeamDisplayNames = [...new Set(consultData.map(item => getTeamDisplayName(item.team_id)))].sort();

    // 转换月度统计为数组格式
    const monthlyStatsArray = Object.entries(monthlyTeamStats)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([month, teams]) => {
        const monthData = { month: month };
        let monthTotal = 0;
        let monthWithSecond = 0;
        let monthBannerStatus1 = 0;

        allTeamDisplayNames.forEach(teamDisplayName => {
          const stats = teams[teamDisplayName] || { total: 0, withSecond: 0, bannerStatus1: 0 };
          monthData[`${teamDisplayName}_报料总数`] = stats.total;
          monthData[`${teamDisplayName}_记者回复`] = stats.withSecond;
          monthData[`${teamDisplayName}_推至问答`] = stats.bannerStatus1;
          monthTotal += stats.total;
          monthWithSecond += stats.withSecond;
          monthBannerStatus1 += stats.bannerStatus1;
        });

        monthData.total = monthTotal;
        monthData.withSecondTotal = monthWithSecond;
        monthData.bannerStatus1Total = monthBannerStatus1;
        return monthData;
      });

    console.log('📊 统计完成');

    res.json({
      success: true,
      data: {
        summary: {
          totalCount: totalCount,
          generatedCount: generatedCount,
          withSecondCount: withSecondCount,
          bannerStatus1Count: bannerStatus1Count,
          pendingCount: totalCount - generatedCount,
          generatedPercentage: totalCount > 0 ? ((generatedCount / totalCount) * 100).toFixed(2) + '%' : '0%',
          withSecondPercentage: totalCount > 0 ? ((withSecondCount / totalCount) * 100).toFixed(2) + '%' : '0%',
          bannerStatus1Percentage: totalCount > 0 ? ((bannerStatus1Count / totalCount) * 100).toFixed(2) + '%' : '0%',
          year: '2025',
          dateRange: {
            start: startDate.toISOString().split('T')[0],
            end: endDate.toISOString().split('T')[0]
          }
        },
        teamStats: teamStatsArray,
        monthlyStats: monthlyStatsArray,
        topTeams: teamStatsArray.slice(0, 10)
      }
    });

  } catch (error) {
    console.error('❌ 统计2025年数据失败:', error);
    res.status(500).json({
      success: false,
      message: '统计数据失败',
      error: error.message
    });
  }
});

// 导出userData用户回复统计（2025年按月区分）
app.get('/api/export-user-reply-stats-2025', async (req, res) => {
  try {
    console.log('📊 开始统计userData用户回复数据（2025年按月区分）...');

    // 设置2025年的时间范围
    const startDate = new Date('2025-01-01T00:00:00.000Z');
    const endDate = new Date('2025-12-31T23:59:59.999Z');

    // 查询2025年的子表数据，条件：reply_user_id ≠ release_user_id
    const secondData = await ConsultSecondModel.find({
      create_time: {
        $gte: startDate,
        $lte: endDate
      },
      reply_user_id: { $ne: '5f22591c77d7193df40023a2' },
      $expr: { $ne: ['$reply_user_id', '$release_user_id'] }
    }).select('reply_user_id create_time');

    console.log(`📋 找到 ${secondData.length} 条2025年的回复数据`);

    // 提取userData中的所有mongo_user_id
    const userIds = userData.map(user => user.mongo_user_id);

    // 创建用户名映射
    const userNameMap = {};
    userData.forEach(user => {
      userNameMap[user.mongo_user_id] = user.publish_name;
    });

    // 按用户和月份统计回复数量
    const userMonthlyStats = {};
    const monthlyTotals = {};

    secondData.forEach(item => {
      const userId = item.reply_user_id;
      const month = item.create_time.toISOString().substring(0, 7); // YYYY-MM

      // 只统计userData中存在的用户
      if (userIds.includes(userId)) {
        if (!userMonthlyStats[userId]) {
          userMonthlyStats[userId] = {
            userName: userNameMap[userId],
            monthlyData: {},
            total: 0
          };
        }

        if (!userMonthlyStats[userId].monthlyData[month]) {
          userMonthlyStats[userId].monthlyData[month] = 0;
        }

        userMonthlyStats[userId].monthlyData[month]++;
        userMonthlyStats[userId].total++;

        // 月度总计
        if (!monthlyTotals[month]) {
          monthlyTotals[month] = 0;
        }
        monthlyTotals[month]++;
      }
    });

    // 获取所有月份并排序
    const allMonths = Object.keys(monthlyTotals).sort();

    // 创建Excel工作簿
    const workbook = XLSX.utils.book_new();

    // 工作表1: 用户月度回复统计
    const userStatsData = [];

    // 添加表头
    const headers = ['用户ID', '用户名称', '总回复数'];
    allMonths.forEach(month => {
      headers.push(`${month}月回复数`);
    });
    headers.push('占比');

    // 计算总回复数
    const totalReplies = Object.values(userMonthlyStats).reduce((sum, user) => sum + user.total, 0);

    // 添加数据行 - 显示所有userData用户，没有回复的显示0
    userData
      .map(user => {
        const userStats = userMonthlyStats[user.mongo_user_id];
        return {
          userId: user.mongo_user_id,
          userName: user.publish_name,
          stats: userStats || { userName: user.publish_name, monthlyData: {}, total: 0 }
        };
      })
      .sort((a, b) => b.stats.total - a.stats.total) // 按总数降序排列
      .forEach(({ userId, userName, stats }) => {
        const row = {
          '用户ID': userId,
          '用户名称': userName,
          '总回复数': stats.total
        };

        allMonths.forEach(month => {
          row[`${month}月回复数`] = stats.monthlyData[month] || 0;
        });

        row['占比'] = totalReplies > 0 ? `${((stats.total / totalReplies) * 100).toFixed(2)}%` : '0%';
        userStatsData.push(row);
      });

    // 添加总计行
    const totalRow = {
      '用户ID': '总计',
      '用户名称': '',
      '总回复数': totalReplies
    };

    allMonths.forEach(month => {
      totalRow[`${month}月回复数`] = monthlyTotals[month] || 0;
    });
    totalRow['占比'] = '100.00%';
    userStatsData.push(totalRow);

    const userStatsSheet = XLSX.utils.json_to_sheet(userStatsData);
    XLSX.utils.book_append_sheet(workbook, userStatsSheet, '用户月度回复统计');

    // 工作表2: 月度汇总统计
    const monthlySummaryData = allMonths.map(month => ({
      '月份': month,
      '回复总数': monthlyTotals[month] || 0,
      '占比': totalReplies > 0 ? `${(((monthlyTotals[month] || 0) / totalReplies) * 100).toFixed(2)}%` : '0%'
    }));

    // 添加年度总计
    monthlySummaryData.push({
      '月份': '2025年总计',
      '回复总数': totalReplies,
      '占比': '100.00%'
    });

    const monthlySummarySheet = XLSX.utils.json_to_sheet(monthlySummaryData);
    XLSX.utils.book_append_sheet(workbook, monthlySummarySheet, '月度汇总');

    // 工作表3: 用户基础信息
    const userInfoData = userData.map(user => {
      const userStats = userMonthlyStats[user.mongo_user_id];
      return {
        '用户ID': user.mongo_user_id,
        '用户名称': user.publish_name,
        '2025年回复数': userStats ? userStats.total : 0,
        '是否活跃': userStats && userStats.total > 0 ? '是' : '否'
      };
    });

    const userInfoSheet = XLSX.utils.json_to_sheet(userInfoData);
    XLSX.utils.book_append_sheet(workbook, userInfoSheet, '用户基础信息');

    // 生成文件名
    const fileName = `user_reply_stats_2025_${new Date().toISOString().split('T')[0]}.xlsx`;

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);

    // 写入并发送文件
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    res.send(buffer);

    console.log(`✅ 用户回复统计Excel导出成功: ${fileName}`);

  } catch (error) {
    console.error('❌ 导出用户回复统计失败:', error);
    res.status(500).json({
      success: false,
      message: '导出用户回复统计失败',
      error: error.message
    });
  }
});

// 获取userData用户回复统计数据（2025年按月区分）
app.get('/api/user-reply-stats-2025', async (req, res) => {
  try {
    console.log('📊 开始获取userData用户回复统计数据（2025年按月区分）...');

    // 设置2025年的时间范围
    const startDate = new Date('2025-01-01T00:00:00.000Z');
    const endDate = new Date('2025-12-31T23:59:59.999Z');

    // 查询2025年的子表数据，条件：reply_user_id ≠ release_user_id
    const secondData = await ConsultSecondModel.find({
      create_time: {
        $gte: startDate,
        $lte: endDate
      },
      reply_user_id: { $ne: '5f22591c77d7193df40023a2' },
      $expr: { $ne: ['$reply_user_id', '$release_user_id'] }
    }).select('reply_user_id create_time');

    console.log(`📋 找到 ${secondData.length} 条2025年的回复数据`);

    // 提取userData中的所有mongo_user_id
    const userIds = userData.map(user => user.mongo_user_id);

    // 创建用户名映射
    const userNameMap = {};
    userData.forEach(user => {
      userNameMap[user.mongo_user_id] = user.publish_name;
    });

    // 按用户和月份统计回复数量
    const userMonthlyStats = {};
    const monthlyTotals = {};

    secondData.forEach(item => {
      const userId = item.reply_user_id;
      const month = item.create_time.toISOString().substring(0, 7); // YYYY-MM

      // 只统计userData中存在的用户
      if (userIds.includes(userId)) {
        if (!userMonthlyStats[userId]) {
          userMonthlyStats[userId] = {
            userName: userNameMap[userId],
            monthlyData: {},
            total: 0
          };
        }

        if (!userMonthlyStats[userId].monthlyData[month]) {
          userMonthlyStats[userId].monthlyData[month] = 0;
        }

        userMonthlyStats[userId].monthlyData[month]++;
        userMonthlyStats[userId].total++;

        // 月度总计
        if (!monthlyTotals[month]) {
          monthlyTotals[month] = 0;
        }
        monthlyTotals[month]++;
      }
    });

    // 获取所有月份并排序
    const allMonths = Object.keys(monthlyTotals).sort();

    // 计算总回复数
    const totalReplies = Object.values(userMonthlyStats).reduce((sum, user) => sum + user.total, 0);

    // 转换为数组格式 - 显示所有userData用户，没有回复的显示0
    const userStatsArray = userData
      .map(user => {
        const userStats = userMonthlyStats[user.mongo_user_id];
        const stats = userStats || { userName: user.publish_name, monthlyData: {}, total: 0 };

        const userStat = {
          userId: user.mongo_user_id,
          userName: user.publish_name,
          totalReplies: stats.total,
          percentage: totalReplies > 0 ? `${((stats.total / totalReplies) * 100).toFixed(2)}%` : '0%',
          monthlyData: {}
        };

        allMonths.forEach(month => {
          userStat.monthlyData[month] = stats.monthlyData[month] || 0;
        });

        return userStat;
      })
      .sort((a, b) => b.totalReplies - a.totalReplies); // 按总数降序排列

    // 月度汇总数据
    const monthlySummaryArray = allMonths.map(month => ({
      month: month,
      totalReplies: monthlyTotals[month] || 0,
      percentage: totalReplies > 0 ? `${(((monthlyTotals[month] || 0) / totalReplies) * 100).toFixed(2)}%` : '0%'
    }));

    // 用户基础信息 - 显示所有userData用户
    const userInfoArray = userData.map(user => {
      const userStats = userMonthlyStats[user.mongo_user_id];
      return {
        userId: user.mongo_user_id,
        userName: user.publish_name,
        totalReplies2025: userStats ? userStats.total : 0,
        isActive: userStats && userStats.total > 0
      };
    }).sort((a, b) => b.totalReplies2025 - a.totalReplies2025); // 按回复数降序排列

    // 返回统计结果
    res.json({
      success: true,
      data: {
        summary: {
          totalReplies: totalReplies,
          activeUsers: Object.keys(userMonthlyStats).length,
          inactiveUsers: userData.length - Object.keys(userMonthlyStats).length,
          totalUsers: userData.length,
          activeRate: userData.length > 0 ? `${((Object.keys(userMonthlyStats).length / userData.length) * 100).toFixed(2)}%` : '0%',
          year: '2025',
          dateRange: {
            start: startDate.toISOString().split('T')[0],
            end: endDate.toISOString().split('T')[0]
          },
          months: allMonths
        },
        userStats: userStatsArray,
        monthlySummary: monthlySummaryArray,
        userInfo: userInfoArray
      }
    });

  } catch (error) {
    console.error('❌ 获取用户回复统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户回复统计数据失败',
      error: error.message
    });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器启动成功，端口: ${PORT}`);
  console.log(`📍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📍 批量插入接口: POST http://localhost:${PORT}/api/batch-insert`);
  console.log(`📍 查询数据接口: GET http://localhost:${PORT}/api/data/:id`);
  console.log(`📍 统计数据接口: GET http://localhost:${PORT}/api/stats`);
  console.log(`📍 集合列表接口: GET http://localhost:${PORT}/api/collections`);
  console.log(`📍 创建集合接口: POST http://localhost:${PORT}/api/create-collection`);
  console.log(`📍 定时任务状态: GET http://localhost:${PORT}/api/cron-status`);
  console.log(`📍 定时任务控制: POST http://localhost:${PORT}/api/cron-control`);
  console.log(`📍 手动触发检查: POST http://localhost:${PORT}/api/trigger-check`);
  console.log(`📍 咨询状态查询: GET http://localhost:${PORT}/api/consult-status`);
  console.log(`📍 重置咨询状态: POST http://localhost:${PORT}/api/reset-consult-status`);
  console.log(`📍 导出2025年数据: GET http://localhost:${PORT}/api/export-consult-2025`);
  console.log(`📍 2025年数据统计: GET http://localhost:${PORT}/api/consult-stats-2025`);
  console.log(`📍 导出2025年数据(含子表): GET http://localhost:${PORT}/api/export-consult-2025-with-second`);
  console.log(`📍 2025年数据统计(含子表): GET http://localhost:${PORT}/api/consult-stats-2025-with-second`);
  console.log(`📍 月度报表控制: POST http://localhost:${PORT}/api/monthly-report-control`);
  console.log(`📍 手动发送月度报表: POST http://localhost:${PORT}/api/send-monthly-report`);
  console.log(`📍 测试邮件连接: POST http://localhost:${PORT}/api/test-email-connection`);
  console.log(`📍 发送测试邮件: POST http://localhost:${PORT}/api/send-test-email`);
  console.log(`📍 获取收件人列表: GET http://localhost:${PORT}/api/recipients`);
  console.log(`📍 添加收件人: POST http://localhost:${PORT}/api/recipients/add`);
  console.log(`📍 移除收件人: POST http://localhost:${PORT}/api/recipients/remove`);
  console.log(`📍 导出用户回复统计: GET http://localhost:${PORT}/api/export-user-reply-stats-2025`);
  console.log(`📍 获取用户回复统计: GET http://localhost:${PORT}/api/user-reply-stats-2025`);

  // 自动启动定时任务
  setTimeout(() => {
    // startCronJob();
    console.log('🕐 定时任务已自动启动，将每10分钟检查一次需要生成点击记录的咨询数据');
    console.log('📋 检查逻辑: open_status=1 且 clicks_generated 字段为空的记录');
    console.log('📅 日期限制: 只处理 create_time >= 2025-06-01 00:00:00 的数据');

    // 启动月度报表定时任务
    startMonthlyReportJob();
    console.log('📧 月度报表定时任务已启动，将在每月28号上午10:00发送报表邮件');
    console.log(`📮 收件人列表: ${RECIPIENTS.join(', ')}`);
  }, 2000);  // 延迟2秒启动，确保服务器完全启动
});

module.exports = app;
