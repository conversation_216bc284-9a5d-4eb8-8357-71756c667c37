# 📊 userData用户回复统计功能说明

## 🎯 功能概述

新增了userData用户在xqbmt_consult_second表中回复数据的统计功能，按2025年月份区分，提供详细的用户活跃度分析。

**重要特性：显示所有userData中的用户，没有进行过回复的用户显示为0，确保完整的用户覆盖。**

## 📋 统计逻辑

### 数据来源
- **主数据**: userData数组中的用户信息
- **统计数据**: xqbmt_consult_second表中的回复记录
- **时间范围**: 2025年全年(2025-01-01 至 2025-12-31)

### 查询条件
```javascript
{
  create_time: { $gte: startDate, $lte: endDate },
  reply_user_id: { $ne: '5f22591c77d7193df40023a2' },
  $expr: { $ne: ['$reply_user_id', '$release_user_id'] }
}
```

**条件说明:**
1. **时间限制**: 只统计2025年的数据
2. **排除系统用户**: reply_user_id ≠ '5f22591c77d7193df40023a2'
3. **排除自回复**: reply_user_id ≠ release_user_id
4. **用户范围**: 只统计userData中存在的用户

## 📱 API接口

### 1. 获取统计数据
```http
GET /api/user-reply-stats-2025
```

**响应格式:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalReplies": 1250,
      "activeUsers": 15,
      "inactiveUsers": 35,
      "totalUsers": 50,
      "activeRate": "30.00%",
      "year": "2025",
      "months": ["2025-01", "2025-02", "..."]
    },
    "userStats": [
      {
        "userId": "5f2b615e380628f2adb291c3",
        "userName": "吴迪律师",
        "totalReplies": 120,
        "percentage": "9.60%",
        "monthlyData": {
          "2025-01": 15,
          "2025-02": 20,
          "..."
        }
      }
    ],
    "monthlySummary": [
      {
        "month": "2025-01",
        "totalReplies": 100,
        "percentage": "8.00%"
      }
    ],
    "userInfo": [
      {
        "userId": "5f2b615e380628f2adb291c3",
        "userName": "吴迪律师",
        "totalReplies2025": 120,
        "isActive": true
      }
    ]
  }
}
```

### 2. 导出Excel文件
```http
GET /api/export-user-reply-stats-2025
```

**功能说明:**
- 生成包含3个工作表的Excel文件
- 自动下载到浏览器
- 文件名格式: `user_reply_stats_2025_YYYY-MM-DD.xlsx`

## 📊 Excel文件结构

### 工作表1: 用户月度回复统计
| 用户ID | 用户名称 | 总回复数 | 2025-01月回复数 | 2025-02月回复数 | ... | 占比 |
|--------|----------|----------|----------------|----------------|-----|------|
| 5f2b615e... | 吴迪律师 | 120 | 15 | 20 | ... | 9.60% |
| 5f334313... | 蒋宙烨 | 95 | 12 | 18 | ... | 7.60% |
| 5f280ab1... | 小强热线帮忙团管理员 | 0 | 0 | 0 | ... | 0.00% |
| ... | ... | ... | ... | ... | ... | ... |
| 总计 | | 1250 | 100 | 150 | ... | 100.00% |

**注意：显示所有userData中的用户，包括没有回复记录的用户（显示为0）**

### 工作表2: 月度汇总统计
| 月份 | 回复总数 | 占比 |
|------|----------|------|
| 2025-01 | 100 | 8.00% |
| 2025-02 | 150 | 12.00% |
| ... | ... | ... |
| 2025年总计 | 1250 | 100.00% |

### 工作表3: 用户基础信息
| 用户ID | 用户名称 | 2025年回复数 | 是否活跃 |
|--------|----------|-------------|----------|
| 5f2b615e... | 吴迪律师 | 120 | 是 |
| 5f334313... | 蒋宙烨 | 95 | 是 |
| 5f280ab1... | 小强热线帮忙团管理员 | 0 | 否 |

## 👥 userData用户信息

### 用户类型
- **律师**: 吴迪律师、戚歆如律师、卫泽玮律师等
- **记者**: 徐超勤记者等
- **老师**: 汪英来老师等
- **管理员**: 小强热线帮忙团管理员等
- **其他专业人士**: 蒋宙烨、葛凤杰、潘跃明等

### 用户总数
- userData数组包含多个用户
- 每个用户有唯一的mongo_user_id和publish_name
- 统计时只计算userData中存在的用户

## 📈 统计维度

### 用户维度
- **总回复数**: 用户在2025年的总回复次数
- **月度分布**: 每月的回复数量
- **活跃度**: 是否有回复记录
- **占比**: 占总回复数的百分比

### 时间维度
- **按月统计**: 2025年每月的回复总数
- **趋势分析**: 月度回复数量变化
- **季度对比**: 可通过月度数据计算季度统计

### 活跃度分析
- **活跃用户**: 2025年有回复记录的用户
- **非活跃用户**: 2025年无回复记录的用户
- **活跃率**: 活跃用户占总用户的比例

## 🧪 测试方法

### 1. 运行测试脚本
```bash
node test-user-reply-stats.js
```

### 2. 手动测试
```bash
# 获取统计数据
curl http://localhost:3000/api/user-reply-stats-2025

# 下载Excel文件
curl -O -J http://localhost:3000/api/export-user-reply-stats-2025
```

### 3. 浏览器测试
- 统计数据: `http://localhost:3000/api/user-reply-stats-2025`
- Excel下载: `http://localhost:3000/api/export-user-reply-stats-2025`

## 📊 数据价值

### 用户管理
- **识别活跃用户**: 找出回复最多的用户
- **发现潜力用户**: 分析用户活跃度变化
- **用户分类**: 按回复数量对用户分级

### 运营分析
- **月度趋势**: 了解用户回复的季节性变化
- **活跃度监控**: 跟踪用户参与度
- **内容质量**: 通过回复数量评估内容吸引力

### 决策支持
- **资源配置**: 根据用户活跃度分配资源
- **激励机制**: 为活跃用户提供奖励
- **产品优化**: 基于用户行为优化产品功能

## ⚠️ 注意事项

### 数据准确性
- 确保userData数组数据完整
- 检查xqbmt_consult_second表的数据质量
- 验证时间字段的格式正确性

### 性能考虑
- 大量数据时可能影响查询速度
- 建议在数据库中为相关字段建立索引
- 考虑分页查询大数据集

### 业务逻辑
- 理解排除条件的业务含义
- 确认统计范围符合业务需求
- 定期验证统计结果的准确性

## 🔄 扩展功能

### 可能的扩展
1. **多年度对比**: 支持不同年份的数据对比
2. **团队统计**: 按用户所属团队进行统计
3. **回复质量**: 结合回复内容长度等指标
4. **实时统计**: 提供实时的用户活跃度监控
5. **趋势预测**: 基于历史数据预测用户活跃度

### 集成建议
1. **定时报表**: 集成到月度报表系统
2. **监控告警**: 设置用户活跃度告警
3. **可视化**: 提供图表展示功能
4. **导出格式**: 支持更多导出格式(PDF、CSV等)
