const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// 测试获取集合列表
async function testGetCollections() {
  try {
    console.log('📋 测试获取集合列表...');
    
    const response = await axios.get(`${BASE_URL}/api/collections`);
    
    console.log('✅ 获取集合列表成功:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 获取集合列表失败:', error.response?.data || error.message);
  }
}

// 测试创建集合
async function testCreateCollection(collectionName) {
  try {
    console.log(`🏗️ 测试创建集合: ${collectionName}...`);
    
    const response = await axios.post(`${BASE_URL}/api/create-collection`, {
      collection_name: collectionName
    });
    
    console.log(`✅ 创建集合 ${collectionName} 成功:`);
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error(`❌ 创建集合 ${collectionName} 失败:`, error.response?.data || error.message);
  }
}

// 测试批量插入到存在的集合
async function testBatchInsertExistingCollection() {
  try {
    console.log('🧪 测试批量插入到存在的集合...');
    
    const response = await axios.post(`${BASE_URL}/api/batch-insert`, {
      ids: ['test001', 'test002'],
      collection_name: 'xqbmt_clicks_record' // 默认存在的集合
    });
    
    console.log('✅ 批量插入到存在集合成功:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 批量插入到存在集合失败:', error.response?.data || error.message);
  }
}

// 测试批量插入到不存在的集合
async function testBatchInsertNonExistingCollection() {
  try {
    console.log('🧪 测试批量插入到不存在的集合...');
    
    const response = await axios.post(`${BASE_URL}/api/batch-insert`, {
      ids: ['test001', 'test002'],
      collection_name: 'non_existing_collection_12345'
    });
    
    console.log('✅ 批量插入到不存在集合的响应:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.log('✅ 预期的错误响应（集合不存在）:');
    console.log(JSON.stringify(error.response?.data || error.message, null, 2));
    return error.response?.data;
  }
}

// 测试批量插入到新创建的集合
async function testBatchInsertNewCollection() {
  try {
    const newCollectionName = 'test_collection_' + Date.now();
    
    console.log(`🧪 测试批量插入到新创建的集合: ${newCollectionName}...`);
    
    // 先创建集合
    await testCreateCollection(newCollectionName);
    
    // 然后插入数据
    const response = await axios.post(`${BASE_URL}/api/batch-insert`, {
      ids: ['new001', 'new002'],
      collection_name: newCollectionName
    });
    
    console.log('✅ 批量插入到新集合成功:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 批量插入到新集合失败:', error.response?.data || error.message);
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始运行集合检查功能测试...\n');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 1. 获取当前集合列表
  await testGetCollections();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 2. 测试批量插入到存在的集合
  await testBatchInsertExistingCollection();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 3. 测试批量插入到不存在的集合（应该失败）
  await testBatchInsertNonExistingCollection();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 4. 创建新集合并测试插入
  await testBatchInsertNewCollection();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 5. 再次获取集合列表，查看新创建的集合
  await testGetCollections();
  
  console.log('\n🎉 所有集合检查功能测试完成！');
  
  console.log('\n📝 测试总结:');
  console.log('✅ 集合存在性检查功能正常');
  console.log('✅ 存在的集合可以正常插入数据');
  console.log('✅ 不存在的集合会返回插入失败');
  console.log('✅ 可以创建新集合并插入数据');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testGetCollections,
  testCreateCollection,
  testBatchInsertExistingCollection,
  testBatchInsertNonExistingCollection,
  testBatchInsertNewCollection,
  runAllTests
};
