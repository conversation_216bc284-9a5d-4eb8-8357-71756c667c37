const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testEmailConfiguration() {
  console.log('📧 开始测试邮件配置...\n');

  try {
    // 1. 测试邮件服务器连接
    console.log('1️⃣ 测试邮件服务器连接...');
    try {
      const connectionResponse = await axios.post(`${BASE_URL}/api/test-email-connection`);
      console.log('✅ 连接测试结果:', connectionResponse.data);
      console.log('');
    } catch (error) {
      console.error('❌ 连接测试失败:', error.response?.data || error.message);
      console.log('');
      
      // 显示常见问题解决方案
      showConnectionTroubleshooting();
      return;
    }

    // 2. 发送测试邮件
    console.log('2️⃣ 发送测试邮件...');
    try {
      const testEmailResponse = await axios.post(`${BASE_URL}/api/send-test-email`);
      console.log('✅ 测试邮件发送成功:', testEmailResponse.data);
      console.log('');
    } catch (error) {
      console.error('❌ 测试邮件发送失败:', error.response?.data || error.message);
      console.log('');
      
      // 显示邮件发送问题解决方案
      showEmailTroubleshooting();
      return;
    }

    // 3. 测试月度报表发送
    console.log('3️⃣ 询问是否测试完整的月度报表发送...');
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise((resolve) => {
      rl.question('是否测试完整的月度报表发送？(y/n): ', (answer) => {
        rl.close();
        resolve(answer.toLowerCase());
      });
    });

    if (answer === 'y' || answer === 'yes') {
      console.log('📊 开始测试月度报表发送...');
      try {
        const reportResponse = await axios.post(`${BASE_URL}/api/send-monthly-report`);
        console.log('✅ 月度报表发送成功:', reportResponse.data);
      } catch (error) {
        console.error('❌ 月度报表发送失败:', error.response?.data || error.message);
      }
    } else {
      console.log('⏭️  跳过月度报表测试');
    }

    console.log('\n✅ 邮件配置测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

function showConnectionTroubleshooting() {
  console.log('🔧 邮件服务器连接问题排查：\n');
  
  console.log('📋 Outlook/Exchange 邮箱配置检查：');
  console.log('1. 确认邮箱服务器地址：smtp.partner.outlook.cn');
  console.log('2. 确认端口：587 (STARTTLS) 或 993 (SSL)');
  console.log('3. 确认用户名：完整邮箱地址');
  console.log('4. 确认密码：邮箱密码或应用专用密码');
  console.log('');
  
  console.log('🌐 网络连接检查：');
  console.log('1. 检查服务器是否能访问外网');
  console.log('2. 检查防火墙是否阻止SMTP端口');
  console.log('3. 检查是否在企业网络环境中需要代理');
  console.log('');
  
  console.log('🔐 认证问题检查：');
  console.log('1. 确认邮箱账号密码正确');
  console.log('2. 检查是否需要启用"安全性较低的应用访问权限"');
  console.log('3. 检查是否需要生成应用专用密码');
  console.log('');
  
  console.log('⚙️  建议的配置修改：');
  console.log('```javascript');
  console.log('const EMAIL_CONFIG = {');
  console.log('  host: "smtp.partner.outlook.cn",');
  console.log('  port: 587,');
  console.log('  secure: false,');
  console.log('  auth: {');
  console.log('    user: "<EMAIL>",');
  console.log('    pass: "your-password"');
  console.log('  },');
  console.log('  tls: {');
  console.log('    rejectUnauthorized: false');
  console.log('  }');
  console.log('};');
  console.log('```');
}

function showEmailTroubleshooting() {
  console.log('🔧 邮件发送问题排查：\n');
  
  console.log('📧 常见邮件发送错误：');
  console.log('1. 认证失败：检查用户名密码');
  console.log('2. 收件人地址无效：检查收件人邮箱格式');
  console.log('3. 邮件内容被拒绝：检查邮件内容是否包含敏感词');
  console.log('4. 发送频率限制：等待一段时间后重试');
  console.log('');
  
  console.log('🔍 调试建议：');
  console.log('1. 检查服务器日志获取详细错误信息');
  console.log('2. 尝试使用其他邮件客户端测试相同配置');
  console.log('3. 联系邮箱服务提供商确认SMTP设置');
  console.log('');
}

function showEmailConfigGuide() {
  console.log('📧 邮件配置指南：\n');
  
  console.log('🏢 企业邮箱 (Exchange/Outlook)：');
  console.log('- 服务器：smtp.partner.outlook.cn');
  console.log('- 端口：587 (STARTTLS) 或 993 (SSL)');
  console.log('- 安全：STARTTLS');
  console.log('- 认证：用户名密码');
  console.log('');
  
  console.log('📮 QQ邮箱：');
  console.log('- 服务器：smtp.qq.com');
  console.log('- 端口：587 或 465');
  console.log('- 安全：SSL/TLS');
  console.log('- 认证：邮箱地址 + 授权码');
  console.log('');
  
  console.log('📬 163邮箱：');
  console.log('- 服务器：smtp.163.com');
  console.log('- 端口：25 或 465');
  console.log('- 安全：SSL/TLS');
  console.log('- 认证：邮箱地址 + 授权码');
  console.log('');
  
  console.log('🔧 环境变量设置：');
  console.log('EMAIL_USER=<EMAIL>');
  console.log('EMAIL_PASS=your-password');
  console.log('RECIPIENT_EMAIL=<EMAIL>');
}

// 主函数
async function main() {
  console.log('🎯 邮件配置测试工具\n');
  
  // 显示配置指南
  showEmailConfigGuide();
  
  // 运行测试
  await testEmailConfiguration();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testEmailConfiguration,
  showConnectionTroubleshooting,
  showEmailTroubleshooting,
  showEmailConfigGuide
};
