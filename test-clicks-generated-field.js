const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// 测试查询咨询状态
async function testConsultStatus(status = null) {
  try {
    let url = `${BASE_URL}/api/consult-status?page=1&limit=10`;
    if (status) {
      url += `&status=${status}`;
    }
    
    console.log(`📊 查询咨询状态 (${status || '全部'})...`);
    
    const response = await axios.get(url);
    
    console.log('✅ 咨询状态查询结果:');
    console.log(`总数: ${response.data.data.pagination.total}`);
    console.log(`当前页: ${response.data.data.pagination.page}/${response.data.data.pagination.pages}`);
    
    if (response.data.data.consults.length > 0) {
      console.log('\n前几条记录:');
      response.data.data.consults.slice(0, 3).forEach((consult, index) => {
        console.log(`${index + 1}. ID: ${consult._id}`);
        console.log(`   open_status: ${consult.open_status}`);
        console.log(`   clicks_generated: ${consult.clicks_generated || '未生成'}`);
        console.log(`   clicks_count: ${consult.clicks_count || '无'}`);
        console.log(`   create_time: ${consult.create_time}`);
        console.log('');
      });
    } else {
      console.log('没有找到符合条件的记录');
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ 查询咨询状态失败:', error.response?.data || error.message);
  }
}

// 测试重置咨询状态
async function testResetConsultStatus(consultIds) {
  try {
    console.log(`🔄 重置咨询状态: ${consultIds.join(', ')}...`);
    
    const response = await axios.post(`${BASE_URL}/api/reset-consult-status`, {
      consult_ids: consultIds
    });
    
    console.log('✅ 重置咨询状态结果:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 重置咨询状态失败:', error.response?.data || error.message);
  }
}

// 测试手动触发检查（验证新逻辑）
async function testTriggerWithNewLogic() {
  try {
    console.log('🔧 手动触发定时任务（测试新的标记字段逻辑）...');
    
    const response = await axios.post(`${BASE_URL}/api/trigger-check`);
    
    console.log('✅ 手动触发结果:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 手动触发失败:', error.response?.data || error.message);
  }
}

// 创建测试数据的说明
function showTestDataInstructions() {
  console.log('💡 创建测试数据说明:');
  console.log('');
  console.log('1. 创建需要生成点击记录的咨询数据:');
  console.log(`
  db.xqbmt_consult.insertOne({
    open_status: 1,
    create_time: new Date(),
    update_time: new Date()
    // 注意：不要添加 clicks_generated 字段
  })
  `);
  
  console.log('2. 创建已生成点击记录的咨询数据:');
  console.log(`
  db.xqbmt_consult.insertOne({
    open_status: 1,
    create_time: new Date(),
    update_time: new Date(),
    clicks_generated: new Date(),
    clicks_count: 423
  })
  `);
  
  console.log('3. 查看现有数据的 clicks_generated 字段:');
  console.log(`
  db.xqbmt_consult.find({open_status: 1}).pretty()
  `);
}

// 运行完整测试
async function runFullTest() {
  console.log('🚀 开始测试 clicks_generated 标记字段功能...\n');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 1. 查询所有咨询状态
  console.log('1️⃣ 查询所有咨询记录状态');
  const allConsults = await testConsultStatus();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 2. 查询待生成点击记录的咨询
  console.log('2️⃣ 查询待生成点击记录的咨询');
  await testConsultStatus('pending');
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 3. 查询已生成点击记录的咨询
  console.log('3️⃣ 查询已生成点击记录的咨询');
  await testConsultStatus('generated');
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 4. 手动触发检查（测试新逻辑）
  console.log('4️⃣ 手动触发检查（测试新的标记字段逻辑）');
  await testTriggerWithNewLogic();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 5. 再次查询状态，看是否有变化
  console.log('5️⃣ 检查执行后的状态变化');
  await testConsultStatus('generated');
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 6. 显示测试数据创建说明
  console.log('6️⃣ 测试数据创建说明');
  showTestDataInstructions();
  
  console.log('\n🎉 clicks_generated 标记字段功能测试完成！');
  
  console.log('\n📝 测试总结:');
  console.log('✅ 咨询状态查询功能正常');
  console.log('✅ 按状态筛选功能正常');
  console.log('✅ 定时任务使用新的标记字段逻辑');
  console.log('✅ 避免重复生成点击记录');
  
  console.log('\n🔍 新逻辑说明:');
  console.log('- 只处理 open_status=1 且 clicks_generated 字段为空的记录');
  console.log('- 成功生成点击记录后，会自动标记 clicks_generated 字段');
  console.log('- 已标记的记录不会被重复处理');
  console.log('- 可以通过重置接口清除标记，用于测试');
}

// 简单测试
async function runSimpleTest() {
  console.log('🧪 运行简单标记字段测试...\n');
  
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('📊 查询待处理的咨询记录:');
  await testConsultStatus('pending');
  
  console.log('\n📊 查询已处理的咨询记录:');
  await testConsultStatus('generated');
  
  console.log('\n🔧 手动触发一次检查:');
  await testTriggerWithNewLogic();
  
  console.log('\n✅ 简单测试完成！');
}

// 测试重置功能
async function testResetFunction() {
  console.log('🧪 测试重置功能...\n');
  
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 先查询已生成的记录
  console.log('📊 查询已生成点击记录的咨询:');
  const generatedConsults = await testConsultStatus('generated');
  
  if (generatedConsults && generatedConsults.data.consults.length > 0) {
    // 取前2条记录进行重置测试
    const consultIds = generatedConsults.data.consults.slice(0, 2).map(c => c._id);
    
    console.log('\n🔄 重置前2条记录的状态:');
    await testResetConsultStatus(consultIds);
    
    console.log('\n📊 重置后查询待处理记录:');
    await testConsultStatus('pending');
  } else {
    console.log('\n💡 没有已生成的记录可供重置测试');
  }
  
  console.log('\n✅ 重置功能测试完成！');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  const testType = process.argv[2] || 'simple';
  
  if (testType === 'full') {
    runFullTest().catch(console.error);
  } else if (testType === 'reset') {
    testResetFunction().catch(console.error);
  } else {
    runSimpleTest().catch(console.error);
  }
}

module.exports = {
  testConsultStatus,
  testResetConsultStatus,
  testTriggerWithNewLogic,
  showTestDataInstructions,
  runFullTest,
  runSimpleTest,
  testResetFunction
};
