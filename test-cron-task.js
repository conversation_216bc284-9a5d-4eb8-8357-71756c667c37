const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// 测试定时任务状态
async function testCronStatus() {
  try {
    console.log('📊 查询定时任务状态...');
    
    const response = await axios.get(`${BASE_URL}/api/cron-status`);
    
    console.log('✅ 定时任务状态:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 查询定时任务状态失败:', error.response?.data || error.message);
  }
}

// 测试启动定时任务
async function testStartCron() {
  try {
    console.log('🚀 启动定时任务...');
    
    const response = await axios.post(`${BASE_URL}/api/cron-control`, {
      action: 'start'
    });
    
    console.log('✅ 启动定时任务结果:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 启动定时任务失败:', error.response?.data || error.message);
  }
}

// 测试停止定时任务
async function testStopCron() {
  try {
    console.log('🛑 停止定时任务...');
    
    const response = await axios.post(`${BASE_URL}/api/cron-control`, {
      action: 'stop'
    });
    
    console.log('✅ 停止定时任务结果:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 停止定时任务失败:', error.response?.data || error.message);
  }
}

// 测试手动触发检查
async function testManualTrigger() {
  try {
    console.log('🔧 手动触发定时任务...');
    
    const response = await axios.post(`${BASE_URL}/api/trigger-check`);
    
    console.log('✅ 手动触发结果:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 手动触发失败:', error.response?.data || error.message);
  }
}

// 模拟创建测试数据（如果有权限的话）
async function createTestConsultData() {
  console.log('💡 注意: 此功能需要直接操作 MongoDB 数据库');
  console.log('💡 您可以手动在 xqbmt_consult 集合中插入测试数据:');
  console.log(`
  示例测试数据:
  {
    "consult_id": "test_consult_001",
    "open_status": 1,
    "create_time": new Date(),
    "update_time": new Date()
  }
  `);
  
  console.log('💡 或者使用 MongoDB 命令:');
  console.log(`
  db.xqbmt_consult.insertOne({
    consult_id: "test_consult_001",
    open_status: 1,
    create_time: new Date(),
    update_time: new Date()
  })
  `);
}

// 运行完整测试
async function runFullTest() {
  console.log('🚀 开始定时任务功能测试...\n');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 1. 查询初始状态
  console.log('1️⃣ 查询定时任务初始状态');
  await testCronStatus();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 2. 测试手动触发
  console.log('2️⃣ 测试手动触发检查');
  await testManualTrigger();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 3. 测试停止定时任务
  console.log('3️⃣ 测试停止定时任务');
  await testStopCron();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 4. 查询停止后状态
  console.log('4️⃣ 查询停止后状态');
  await testCronStatus();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 5. 测试重新启动
  console.log('5️⃣ 测试重新启动定时任务');
  await testStartCron();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 6. 查询最终状态
  console.log('6️⃣ 查询最终状态');
  await testCronStatus();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 7. 显示测试数据创建说明
  console.log('7️⃣ 测试数据创建说明');
  await createTestConsultData();
  
  console.log('\n🎉 定时任务功能测试完成！');
  
  console.log('\n📝 测试总结:');
  console.log('✅ 定时任务状态查询功能正常');
  console.log('✅ 定时任务启动/停止控制正常');
  console.log('✅ 手动触发检查功能正常');
  console.log('✅ 定时任务会每10分钟自动执行一次');
  
  console.log('\n🔍 监控建议:');
  console.log('- 查看服务器日志观察定时任务执行情况');
  console.log('- 在 xqbmt_consult 集合中添加 open_status=1 的测试数据');
  console.log('- 观察是否自动触发批量插入操作');
}

// 简单测试（只测试基本功能）
async function runSimpleTest() {
  console.log('🧪 运行简单定时任务测试...\n');
  
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('📊 查询定时任务状态:');
  await testCronStatus();
  
  console.log('\n🔧 手动触发一次检查:');
  await testManualTrigger();
  
  console.log('\n✅ 简单测试完成！');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  const testType = process.argv[2] || 'simple';
  
  if (testType === 'full') {
    runFullTest().catch(console.error);
  } else {
    runSimpleTest().catch(console.error);
  }
}

module.exports = {
  testCronStatus,
  testStartCron,
  testStopCron,
  testManualTrigger,
  createTestConsultData,
  runFullTest,
  runSimpleTest
};
