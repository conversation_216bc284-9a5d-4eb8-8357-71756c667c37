const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// 测试默认集合插入
async function testDefaultCollectionInsert() {
  try {
    console.log('🧪 测试插入到默认集合（不指定collection_name）...');
    
    const response = await axios.post(`${BASE_URL}/api/batch-insert`, {
      ids: ['default_test_001', 'default_test_002']
    });
    
    console.log('✅ 默认集合插入成功:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 默认集合插入失败:', error.response?.data || error.message);
  }
}

// 测试指定集合插入
async function testSpecificCollectionInsert() {
  try {
    console.log('🧪 测试插入到指定集合（xqbmt_clicks_record）...');
    
    const response = await axios.post(`${BASE_URL}/api/batch-insert`, {
      ids: ['specific_test_001', 'specific_test_002'],
      collection_name: 'xqbmt_clicks_record'
    });
    
    console.log('✅ 指定集合插入成功:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 指定集合插入失败:', error.response?.data || error.message);
  }
}

// 获取集合列表并检查
async function checkCollections() {
  try {
    console.log('📋 检查当前所有集合...');
    
    const response = await axios.get(`${BASE_URL}/api/collections`);
    
    console.log('✅ 当前集合列表:');
    console.log(JSON.stringify(response.data, null, 2));
    
    // 检查是否有意外的复数化集合
    const collections = response.data.data.collections;
    const hasCorrectCollection = collections.includes('xqbmt_clicks_record');
    const hasWrongCollection = collections.includes('xqbmt_clicks_records');
    
    console.log('\n🔍 集合名称检查:');
    console.log(`✅ 正确集合 'xqbmt_clicks_record' 存在: ${hasCorrectCollection}`);
    console.log(`❌ 错误集合 'xqbmt_clicks_records' 存在: ${hasWrongCollection}`);
    
    if (hasWrongCollection) {
      console.log('⚠️  警告: 发现了复数化的集合名称，这可能是Mongoose自动复数化导致的');
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ 获取集合列表失败:', error.response?.data || error.message);
  }
}

// 直接查询MongoDB验证数据位置
async function verifyDataLocation() {
  try {
    console.log('🔍 验证数据实际存储位置...');
    
    // 查询默认集合中的数据
    const response = await axios.get(`${BASE_URL}/api/data/default_test_001`);
    
    console.log('✅ 查询结果:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('❌ 查询数据失败:', error.response?.data || error.message);
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始验证集合名称正确性测试...\n');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 1. 检查初始集合状态
  console.log('1️⃣ 检查初始集合状态');
  await checkCollections();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 2. 测试默认集合插入
  console.log('2️⃣ 测试默认集合插入');
  await testDefaultCollectionInsert();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 3. 测试指定集合插入
  console.log('3️⃣ 测试指定集合插入');
  await testSpecificCollectionInsert();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 4. 再次检查集合状态
  console.log('4️⃣ 检查插入后的集合状态');
  await checkCollections();
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 5. 验证数据位置
  console.log('5️⃣ 验证数据存储位置');
  await verifyDataLocation();
  
  console.log('\n🎉 集合名称验证测试完成！');
  
  console.log('\n📝 测试总结:');
  console.log('✅ 验证数据是否插入到正确的集合 xqbmt_clicks_record');
  console.log('✅ 检查是否避免了Mongoose的自动复数化问题');
  console.log('✅ 确认默认集合和指定集合的行为一致');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testDefaultCollectionInsert,
  testSpecificCollectionInsert,
  checkCollections,
  verifyDataLocation,
  runAllTests
};
