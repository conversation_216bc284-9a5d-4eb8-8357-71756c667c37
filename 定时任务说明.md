# 定时任务功能说明

## 🎯 功能概述

系统新增了定时任务功能，每10分钟自动检查 `xqbmt_consult` 集合中新增的 `open_status=1` 的数据，并自动调用批量插入接口生成点击记录。

## ⏰ 定时任务配置

- **执行频率**: 每10分钟执行一次
- **Cron 表达式**: `*/10 * * * *`
- **自动启动**: 服务器启动后2秒自动开始运行
- **并发控制**: 防止多个任务同时执行

## 🔍 检查逻辑

### 数据查询条件
```javascript
{
  open_status: 1,
  create_time: { $gte: new Date('2025-06-01T00:00:00.000Z') },  // 只检查2025-06-01之后的数据
  $or: [
    { clicks_generated: { $exists: false } },  // 字段不存在
    { clicks_generated: null },                // 字段为 null
    { clicks_generated: { $eq: "" } }          // 字段为空字符串
  ]
}
```

### 处理流程
1. **日期过滤**: 只查询 `create_time >= 2025-06-01 00:00:00` 的数据
2. **状态筛选**: 查询 `open_status=1` 且 `clicks_generated` 字段为空的数据
3. 提取 `_id` 作为批量插入的标识
4. 调用批量插入接口生成300-500条随机点击记录
5. **成功后自动标记**: 更新 `clicks_generated` 字段为当前时间
6. 记录 `clicks_count` 字段为生成的点击记录数量

### 🔄 避免重复处理
- 使用 `clicks_generated` 字段作为标记
- 已标记的记录不会被重复处理
- 确保每个咨询只生成一次点击记录

### 📅 日期限制
- **只处理2025-06-01之后的数据**: 避免处理历史数据
- 提高查询效率，减少不必要的数据处理
- 所有相关接口都应用相同的日期限制

## 📡 API 接口

### 1. 查询定时任务状态
```http
GET /api/cron-status
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "isRunning": true,
    "isTaskExecuting": false,
    "lastCheckTime": "2025-06-27T08:30:00.000Z",
    "schedule": "每10分钟执行一次"
  }
}
```

### 2. 控制定时任务启停
```http
POST /api/cron-control
Content-Type: application/json

{
  "action": "start"  // 或 "stop"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "定时任务启动成功",
  "data": {
    "action": "start",
    "result": true,
    "isRunning": true
  }
}
```

### 3. 手动触发检查
```http
POST /api/trigger-check
```

**响应示例:**
```json
{
  "success": true,
  "message": "手动检查完成",
  "timestamp": "2025-06-27T08:35:00.000Z"
}
```

### 4. 查询咨询状态
```http
GET /api/consult-status?page=1&limit=20&status=pending
```

**参数说明:**
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `status`: 状态筛选
  - `pending`: 待生成点击记录
  - `generated`: 已生成点击记录
  - 不传则查询全部

**响应示例:**
```json
{
  "success": true,
  "data": {
    "consults": [
      {
        "_id": "507f1f77bcf86cd799439011",
        "open_status": 1,
        "clicks_generated": "2025-06-27T08:30:00.000Z",
        "clicks_count": 423,
        "create_time": "2025-06-27T08:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 5,
      "pages": 1
    }
  }
}
```

### 5. 重置咨询状态（测试用）
```http
POST /api/reset-consult-status
Content-Type: application/json

{
  "consult_ids": ["507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012"]
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "成功重置 2 条记录的状态",
  "data": {
    "modifiedCount": 2
  }
}
```

## 🗄️ 数据库结构

### xqbmt_consult 集合
定时任务会查询此集合，期望的字段结构：
```javascript
{
  "_id": ObjectId,
  "open_status": Number,       // 状态：1=需要生成点击记录
  "create_time": Date,         // 创建时间
  "update_time": Date,         // 更新时间
  "clicks_generated": Date,    // 点击记录生成时间（标记字段）
  "clicks_count": Number       // 生成的点击记录数量
}
```

**字段说明:**
- `clicks_generated`: 标记字段，有值表示已生成过点击记录
- `clicks_count`: 记录本次生成的点击记录数量
- 只有 `clicks_generated` 为空的记录才会被处理

### xqbmt_clicks_record 集合
批量插入的目标集合，每个咨询ID会生成300-500条记录：
```javascript
{
  "_id": ObjectId,
  "consult_id": String,        // 来源咨询ID
  "user_id": String,
  "del_flag": Number,
  "source": Number,
  "create_time": Date,
  "update_time": Date,
  "second_id": String
}
```

## 📊 日志监控

### 正常执行日志
```
🕐 [2025-06-27T08:30:00.000Z] 开始检查需要生成点击记录的咨询数据...
📅 日期限制: 只检查 2025-06-01T00:00:00.000Z 之后创建的数据
🔍 发现 2 条需要生成点击记录的咨询数据
📋 准备批量插入的 IDs: 507f1f77bcf86cd799439011, 507f1f77bcf86cd799439012
   1. ID: 507f1f77bcf86cd799439011, 创建时间: 2025-06-15T10:30:00.000Z
   2. ID: 507f1f77bcf86cd799439012, 创建时间: 2025-06-20T14:20:00.000Z
✅ 批量插入成功: 847 条数据
✅ 已标记 2 条记录为已生成点击记录
📊 总共生成 847 条点击记录
✅ 定时任务完成，检查时间: 2025-06-27T08:30:15.000Z
```

### 无新数据日志
```
🕐 [2025-06-27T08:40:00.000Z] 开始检查需要生成点击记录的咨询数据...
📅 日期限制: 只检查 2025-06-01T00:00:00.000Z 之后创建的数据
ℹ️  没有发现需要生成点击记录的咨询数据 (2025-06-01之后且未处理)
✅ 定时任务完成，检查时间: 2025-06-27T08:40:05.000Z
```

### 错误日志
```
❌ 定时任务执行失败: Error message
❌ 调用批量插入API失败: Error details
```

## 🧪 测试方法

### 1. 运行测试脚本
```bash
# 简单测试
node test-cron-task.js

# 完整测试
node test-cron-task.js full

# 测试标记字段功能
node test-clicks-generated-field.js

# 完整标记字段测试
node test-clicks-generated-field.js full

# 测试重置功能
node test-clicks-generated-field.js reset
```

### 2. 手动创建测试数据
在 MongoDB 中插入测试数据：

**创建需要生成点击记录的数据:**
```javascript
db.xqbmt_consult.insertOne({
  open_status: 1,
  create_time: new Date(),
  update_time: new Date()
  // 注意：不要添加 clicks_generated 字段
})
```

**创建已生成点击记录的数据:**
```javascript
db.xqbmt_consult.insertOne({
  open_status: 1,
  create_time: new Date(),
  update_time: new Date(),
  clicks_generated: new Date(),
  clicks_count: 423
})
```

### 3. 观察执行结果
- 查看服务器控制台日志
- 检查 `xqbmt_clicks_record` 集合中是否生成了新数据
- 使用 API 查询定时任务状态

## ⚙️ 配置选项

### 修改执行频率
在 `server.js` 中修改 cron 表达式：
```javascript
// 每5分钟执行一次
cronJob = cron.schedule('*/5 * * * *', async () => {
  // ...
});

// 每小时执行一次
cronJob = cron.schedule('0 * * * *', async () => {
  // ...
});
```

### 修改查询条件
在 `checkNewConsultData` 函数中调整查询逻辑：
```javascript
const newConsults = await ConsultModel.find({
  open_status: 1,
  // 添加其他查询条件
  // status: 'active',
  // type: 'consultation'
});
```

## 🔧 故障排除

### 常见问题

1. **定时任务未启动**
   - 检查服务器启动日志
   - 手动调用启动接口：`POST /api/cron-control {"action": "start"}`

2. **没有检测到新数据**
   - 确认 `xqbmt_consult` 集合存在
   - 检查数据的 `open_status` 字段值
   - 确认 `create_time` 或 `update_time` 在最后检查时间之后

3. **批量插入失败**
   - 检查 `xqbmt_clicks_record` 集合是否存在
   - 确认数据库连接正常
   - 查看详细错误日志

### 调试步骤

1. 查询定时任务状态：`GET /api/cron-status`
2. 手动触发检查：`POST /api/trigger-check`
3. 查看服务器控制台日志
4. 检查数据库中的数据状态

## 📈 性能考虑

- **查询优化**: 建议在 `open_status`、`create_time`、`update_time` 字段上创建索引
- **批量插入**: 使用 MongoDB 的 `insertMany` 进行高效批量插入
- **并发控制**: 防止多个定时任务同时执行
- **错误处理**: 单次失败不会影响后续执行

## 🔒 安全注意事项

- 定时任务只读取数据，不修改 `xqbmt_consult` 集合
- 批量插入使用内部 API 调用，保持数据一致性
- 所有操作都有详细日志记录，便于审计和调试
