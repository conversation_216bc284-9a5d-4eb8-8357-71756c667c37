# 定时任务功能说明

## 🎯 功能概述

系统新增了定时任务功能，每10分钟自动检查 `xqbmt_consult` 集合中新增的 `open_status=1` 的数据，并自动调用批量插入接口生成点击记录。

## ⏰ 定时任务配置

- **执行频率**: 每10分钟执行一次
- **Cron 表达式**: `*/10 * * * *`
- **自动启动**: 服务器启动后2秒自动开始运行
- **并发控制**: 防止多个任务同时执行

## 🔍 检查逻辑

### 数据查询条件
```javascript
{
  open_status: 1,
  $or: [
    { create_time: { $gt: lastCheckTime } },
    { update_time: { $gt: lastCheckTime } }
  ]
}
```

### 处理流程
1. 查询自上次检查以来新增或更新的 `open_status=1` 数据
2. 提取 `consult_id` 或 `_id` 作为批量插入的标识
3. 调用批量插入接口生成300-500条随机点击记录
4. 更新最后检查时间戳

## 📡 API 接口

### 1. 查询定时任务状态
```http
GET /api/cron-status
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "isRunning": true,
    "isTaskExecuting": false,
    "lastCheckTime": "2025-06-27T08:30:00.000Z",
    "schedule": "每10分钟执行一次"
  }
}
```

### 2. 控制定时任务启停
```http
POST /api/cron-control
Content-Type: application/json

{
  "action": "start"  // 或 "stop"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "定时任务启动成功",
  "data": {
    "action": "start",
    "result": true,
    "isRunning": true
  }
}
```

### 3. 手动触发检查
```http
POST /api/trigger-check
```

**响应示例:**
```json
{
  "success": true,
  "message": "手动检查完成",
  "timestamp": "2025-06-27T08:35:00.000Z"
}
```

## 🗄️ 数据库结构

### xqbmt_consult 集合
定时任务会查询此集合，期望的字段结构：
```javascript
{
  "_id": ObjectId,
  "consult_id": String,        // 咨询ID（优先使用）
  "open_status": Number,       // 状态：1=需要生成点击记录
  "create_time": Date,         // 创建时间
  "update_time": Date          // 更新时间
}
```

### xqbmt_clicks_record 集合
批量插入的目标集合，每个咨询ID会生成300-500条记录：
```javascript
{
  "_id": ObjectId,
  "consult_id": String,        // 来源咨询ID
  "user_id": String,
  "del_flag": Number,
  "source": Number,
  "create_time": Date,
  "update_time": Date,
  "second_id": String
}
```

## 📊 日志监控

### 正常执行日志
```
🕐 [2025-06-27T08:30:00.000Z] 开始检查新的咨询数据...
🔍 发现 2 条新的咨询数据 (open_status=1)
📋 准备批量插入的 IDs: consult001, consult002
✅ 批量插入成功: 847 条数据
📊 插入详情: [{"id":"consult001","count":423},{"id":"consult002","count":424}]
✅ 定时任务完成，更新检查时间: 2025-06-27T08:30:15.000Z
```

### 无新数据日志
```
🕐 [2025-06-27T08:40:00.000Z] 开始检查新的咨询数据...
ℹ️  没有发现新的咨询数据 (open_status=1)
```

### 错误日志
```
❌ 定时任务执行失败: Error message
❌ 调用批量插入API失败: Error details
```

## 🧪 测试方法

### 1. 运行测试脚本
```bash
# 简单测试
node test-cron-task.js

# 完整测试
node test-cron-task.js full
```

### 2. 手动创建测试数据
在 MongoDB 中插入测试数据：
```javascript
db.xqbmt_consult.insertOne({
  consult_id: "test_consult_001",
  open_status: 1,
  create_time: new Date(),
  update_time: new Date()
})
```

### 3. 观察执行结果
- 查看服务器控制台日志
- 检查 `xqbmt_clicks_record` 集合中是否生成了新数据
- 使用 API 查询定时任务状态

## ⚙️ 配置选项

### 修改执行频率
在 `server.js` 中修改 cron 表达式：
```javascript
// 每5分钟执行一次
cronJob = cron.schedule('*/5 * * * *', async () => {
  // ...
});

// 每小时执行一次
cronJob = cron.schedule('0 * * * *', async () => {
  // ...
});
```

### 修改查询条件
在 `checkNewConsultData` 函数中调整查询逻辑：
```javascript
const newConsults = await ConsultModel.find({
  open_status: 1,
  // 添加其他查询条件
  // status: 'active',
  // type: 'consultation'
});
```

## 🔧 故障排除

### 常见问题

1. **定时任务未启动**
   - 检查服务器启动日志
   - 手动调用启动接口：`POST /api/cron-control {"action": "start"}`

2. **没有检测到新数据**
   - 确认 `xqbmt_consult` 集合存在
   - 检查数据的 `open_status` 字段值
   - 确认 `create_time` 或 `update_time` 在最后检查时间之后

3. **批量插入失败**
   - 检查 `xqbmt_clicks_record` 集合是否存在
   - 确认数据库连接正常
   - 查看详细错误日志

### 调试步骤

1. 查询定时任务状态：`GET /api/cron-status`
2. 手动触发检查：`POST /api/trigger-check`
3. 查看服务器控制台日志
4. 检查数据库中的数据状态

## 📈 性能考虑

- **查询优化**: 建议在 `open_status`、`create_time`、`update_time` 字段上创建索引
- **批量插入**: 使用 MongoDB 的 `insertMany` 进行高效批量插入
- **并发控制**: 防止多个定时任务同时执行
- **错误处理**: 单次失败不会影响后续执行

## 🔒 安全注意事项

- 定时任务只读取数据，不修改 `xqbmt_consult` 集合
- 批量插入使用内部 API 调用，保持数据一致性
- 所有操作都有详细日志记录，便于审计和调试
