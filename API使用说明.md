# API 使用说明 - 集合检查功能

## 新增功能概述

现在批量插入接口支持**表单（集合）存在性检查**：
- ✅ 如果指定的集合存在，则执行批量插入
- ❌ 如果指定的集合不存在，则返回插入失败

## API 接口详情

### 1. 批量插入数据（带集合检查）

```http
POST /api/batch-insert
Content-Type: application/json

{
  "ids": ["consult001", "consult002", "consult003"],
  "collection_name": "xqbmt_clicks_record"  // 可选，默认为 xqbmt_clicks_record
}
```

**参数说明：**
- `ids` (必需): 要插入数据的ID数组
- `collection_name` (可选): 目标集合名称，默认为 `xqbmt_clicks_record`

**成功响应示例：**
```json
{
  "success": true,
  "message": "批量插入成功",
  "data": {
    "collection": "xqbmt_clicks_record",
    "totalInserted": 1247,
    "insertSummary": [
      {"id": "consult001", "count": 423},
      {"id": "consult002", "count": 387},
      {"id": "consult003", "count": 437}
    ],
    "executionTime": "15ms"
  }
}
```

**失败响应示例（集合不存在）：**
```json
{
  "success": false,
  "message": "表单不存在: non_existing_collection",
  "error": "COLLECTION_NOT_EXISTS"
}
```

### 2. 获取集合列表

```http
GET /api/collections
```

**响应示例：**
```json
{
  "success": true,
  "message": "获取集合列表成功",
  "data": {
    "collections": [
      "xqbmt_clicks_record",
      "users",
      "products"
    ],
    "count": 3
  }
}
```

### 3. 创建集合

```http
POST /api/create-collection
Content-Type: application/json

{
  "collection_name": "new_collection_name"
}
```

**响应示例（新建）：**
```json
{
  "success": true,
  "message": "集合创建成功: new_collection_name",
  "data": {
    "collection": "new_collection_name",
    "action": "created"
  }
}
```

**响应示例（已存在）：**
```json
{
  "success": true,
  "message": "集合已存在: existing_collection",
  "data": {
    "collection": "existing_collection",
    "action": "already_exists"
  }
}
```

## 使用流程示例

### 场景1：插入到已知存在的集合
```bash
# 1. 直接插入到默认集合
curl -X POST http://localhost:3000/api/batch-insert \
  -H "Content-Type: application/json" \
  -d '{"ids": ["consult001", "consult002"]}'
```

### 场景2：检查集合是否存在
```bash
# 1. 获取所有集合列表
curl http://localhost:3000/api/collections

# 2. 插入到指定集合
curl -X POST http://localhost:3000/api/batch-insert \
  -H "Content-Type: application/json" \
  -d '{"ids": ["consult001"], "collection_name": "xqbmt_clicks_record"}'
```

### 场景3：创建新集合并插入数据
```bash
# 1. 创建新集合
curl -X POST http://localhost:3000/api/create-collection \
  -H "Content-Type: application/json" \
  -d '{"collection_name": "new_data_collection"}'

# 2. 插入数据到新集合
curl -X POST http://localhost:3000/api/batch-insert \
  -H "Content-Type: application/json" \
  -d '{"ids": ["data001", "data002"], "collection_name": "new_data_collection"}'
```

## 数据结构

插入的每条数据结构：
```json
{
  "_id": "ObjectId",
  "consult_id": "consult001",
  "second_id": null,
  "del_flag": 0,
  "source": 0,
  "create_time": "2025-06-27T06:30:00.000Z",
  "update_time": "2025-06-27T06:30:00.000Z",
  "user_id": "5f22591c77d7193df40023a2"
}
```

## 错误处理

### 常见错误类型：

1. **集合不存在**
   ```json
   {
     "success": false,
     "message": "表单不存在: collection_name",
     "error": "COLLECTION_NOT_EXISTS"
   }
   ```

2. **参数错误**
   ```json
   {
     "success": false,
     "message": "ids参数必须是非空数组"
   }
   ```

3. **数据库连接错误**
   ```json
   {
     "success": false,
     "message": "批量插入失败",
     "error": "具体错误信息"
   }
   ```

## 测试

运行集合检查功能测试：
```bash
node test-collection-check.js
```

测试将验证：
- ✅ 获取集合列表功能
- ✅ 创建新集合功能
- ✅ 插入到存在集合的功能
- ✅ 插入到不存在集合时的错误处理
- ✅ 创建集合后插入数据的完整流程

## 注意事项

1. **默认集合**: 如果不指定 `collection_name`，默认使用 `xqbmt_clicks_record`
2. **集合检查**: 每次批量插入前都会检查目标集合是否存在
3. **数据量**: 每个ID仍然会随机生成300-500条数据
4. **权限**: 确保数据库用户有创建集合和插入数据的权限
5. **性能**: 集合存在性检查会增加少量延迟，但确保数据安全性
