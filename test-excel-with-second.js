const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

// 测试获取2025年统计数据（包含子表）
async function testConsultStatsWithSecond() {
  try {
    console.log('📊 获取2025年咨询数据统计（包含子表统计）...');
    
    const response = await axios.get(`${BASE_URL}/api/consult-stats-2025-with-second`);
    
    console.log('✅ 2025年数据统计结果（包含子表）:');
    console.log('');
    
    const data = response.data.data;
    
    // 显示总体统计
    console.log('📈 总体统计:');
    console.log(`   总数据量: ${data.summary.totalCount} 条`);
    console.log(`   已生成点击记录: ${data.summary.generatedCount} 条 (${data.summary.generatedPercentage})`);
    console.log(`   有子表记录: ${data.summary.withSecondCount} 条 (${data.summary.withSecondPercentage})`);
    console.log(`   banner_status=1: ${data.summary.bannerStatus1Count} 条 (${data.summary.bannerStatus1Percentage})`);
    console.log(`   待生成点击记录: ${data.summary.pendingCount} 条`);
    console.log(`   统计年份: ${data.summary.year}`);
    console.log(`   日期范围: ${data.summary.dateRange.start} 至 ${data.summary.dateRange.end}`);
    console.log('');
    
    // 显示Team统计（前10个）
    console.log('🏢 Team统计 (前10个，包含子表和banner统计):');
    data.topTeams.forEach((team, index) => {
      console.log(`   ${index + 1}. ${team.teamName} (${team.teamId})`);
      console.log(`      总数: ${team.totalCount} 条 (${team.percentage})`);
      console.log(`      已生成点击记录: ${team.generatedCount} 条 (${team.generatedPercentage})`);
      console.log(`      有子表记录: ${team.withSecondCount} 条 (${team.withSecondPercentage})`);
      console.log(`      banner_status=1: ${team.bannerStatus1Count} 条 (${team.bannerStatus1Percentage})`);
      console.log('');
    });
    
    // 显示月度统计（包含子表和banner统计）
    console.log('📅 月度Team统计（包含子表和banner统计）:');
    data.monthlyStats.forEach(month => {
      console.log(`   ${month.month}:`);
      Object.keys(month).forEach(key => {
        if (key !== 'month' && key !== 'total' && key !== 'withSecondTotal' && key !== 'bannerStatus1Total') {
          if (key.endsWith('_total')) {
            const teamId = key.replace('_total', '');
            const withSecondKey = `${teamId}_withSecond`;
            const bannerStatus1Key = `${teamId}_bannerStatus1`;
            console.log(`      ${teamId}: 总数 ${month[key]} 条, 有子表 ${month[withSecondKey] || 0} 条, banner状态1 ${month[bannerStatus1Key] || 0} 条`);
          }
        }
      });
      console.log(`      月度总计: ${month.total} 条, 有子表总计: ${month.withSecondTotal} 条, banner状态1总计: ${month.bannerStatus1Total} 条`);
      console.log('');
    });
    
    return response.data;
  } catch (error) {
    console.error('❌ 获取统计数据失败:', error.response?.data || error.message);
  }
}

// 测试下载Excel文件（包含子表）
async function testExportExcelWithSecond() {
  try {
    console.log('📥 开始下载2025年咨询数据Excel文件（包含子表统计）...');
    
    const response = await axios.get(`${BASE_URL}/api/export-consult-2025-with-second`, {
      responseType: 'stream'
    });
    
    // 获取文件名
    const contentDisposition = response.headers['content-disposition'];
    let fileName = 'xqbmt_consult_2025_with_second.xlsx';
    
    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (fileNameMatch && fileNameMatch[1]) {
        fileName = decodeURIComponent(fileNameMatch[1].replace(/['"]/g, ''));
      }
    }
    
    // 保存文件
    const downloadPath = path.join(__dirname, 'downloads');
    if (!fs.existsSync(downloadPath)) {
      fs.mkdirSync(downloadPath, { recursive: true });
    }
    
    const filePath = path.join(downloadPath, fileName);
    const writer = fs.createWriteStream(filePath);
    
    response.data.pipe(writer);
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log(`✅ Excel文件下载成功: ${filePath}`);
        
        // 检查文件大小
        const stats = fs.statSync(filePath);
        console.log(`📁 文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
        
        resolve(filePath);
      });
      
      writer.on('error', (error) => {
        console.error('❌ 文件写入失败:', error);
        reject(error);
      });
    });
    
  } catch (error) {
    console.error('❌ 下载Excel文件失败:', error.response?.data || error.message);
  }
}

// 创建测试数据的说明
function showTestDataInstructions() {
  console.log('💡 创建2025年测试数据说明（包含子表数据）:');
  console.log('');
  console.log('1. 创建主表数据 (xqbmt_consult):');
  console.log(`
  db.xqbmt_consult.insertMany([
    {
      _id: ObjectId("60f1234567890123456789a1"),
      team_id: "5f0416f1e2bfa86f8c797c36",  // 法律帮
      open_status: 1,
      banner_status: 1,
      consult_title: "咨询标题1",
      create_time: new Date("2025-01-15T10:30:00.000Z"),
      update_time: new Date()
    },
    {
      _id: ObjectId("60f1234567890123456789a2"),
      team_id: "5f04170ae2bfa86f8c797c37",  // 汽车帮
      open_status: 1,
      banner_status: 0,
      consult_title: "咨询标题2",
      create_time: new Date("2025-02-20T14:20:00.000Z"),
      update_time: new Date()
    },
    {
      _id: ObjectId("60f1234567890123456789a3"),
      team_id: "5f3ba44077d7190bf80077c9",  // 新闻爆料
      open_status: 1,
      banner_status: 1,
      consult_title: "咨询标题3",
      create_time: new Date("2025-03-10T09:15:00.000Z"),
      update_time: new Date()
    }
  ])
  `);
  
  console.log('2. 创建子表数据 (xqbmt_consult_second):');
  console.log(`
  db.xqbmt_consult_second.insertMany([
    {
      consult_id: "60f1234567890123456789a1",
      reply_user_id: "user001",  // 不等于 5f22591c77d7193df40023a2
      release_user_id: "user999",  // 不等于 reply_user_id，会被统计
      create_time: new Date("2025-01-15T11:00:00.000Z"),
      update_time: new Date()
    },
    {
      consult_id: "60f1234567890123456789a1",
      reply_user_id: "user002",  // 不等于 5f22591c77d7193df40023a2
      release_user_id: "user002",  // 等于 reply_user_id，不会被统计
      create_time: new Date("2025-01-15T12:00:00.000Z"),
      update_time: new Date()
    },
    {
      consult_id: "60f1234567890123456789a2",
      reply_user_id: "5f22591c77d7193df40023a2",  // 等于指定值，不会被统计
      release_user_id: "user888",
      create_time: new Date("2025-02-20T15:00:00.000Z"),
      update_time: new Date()
    },
    {
      consult_id: "60f1234567890123456789a3",
      reply_user_id: "user003",  // 不等于 5f22591c77d7193df40023a2
      release_user_id: "user777",  // 不等于 reply_user_id，会被统计
      create_time: new Date("2025-03-10T10:00:00.000Z"),
      update_time: new Date()
    }
  ])
  `);
  
  console.log('3. 查询验证:');
  console.log(`
  // 查看主表数据
  db.xqbmt_consult.find({
    create_time: {
      $gte: new Date("2025-01-01T00:00:00.000Z"),
      $lte: new Date("2025-12-31T23:59:59.999Z")
    }
  }).pretty()

  // 查看子表数据（符合所有条件）
  db.xqbmt_consult_second.find({
    reply_user_id: { $ne: "5f22591c77d7193df40023a2" },
    $expr: { $ne: ["$reply_user_id", "$release_user_id"] }
  }).pretty()

  // 统计每个consult_id的子表记录数（新查询条件）
  db.xqbmt_consult_second.aggregate([
    {
      $match: {
        reply_user_id: { $ne: "5f22591c77d7193df40023a2" },
        $expr: { $ne: ["$reply_user_id", "$release_user_id"] }
      }
    },
    {
      $group: {
        _id: "$consult_id",
        count: { $sum: 1 }
      }
    }
  ])
  `);
}

// 运行完整测试
async function runFullTest() {
  console.log('🚀 开始测试2025年数据导出功能（包含子表统计）...\n');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 1. 获取统计数据
  console.log('1️⃣ 获取2025年数据统计（包含子表）');
  const statsResult = await testConsultStatsWithSecond();
  console.log('\n' + '='.repeat(80) + '\n');
  
  // 2. 下载Excel文件
  console.log('2️⃣ 下载Excel文件（包含子表统计）');
  const filePath = await testExportExcelWithSecond();
  console.log('\n' + '='.repeat(80) + '\n');
  
  // 3. 显示测试数据创建说明
  console.log('3️⃣ 测试数据创建说明');
  showTestDataInstructions();
  
  console.log('\n🎉 2025年数据导出功能测试完成（包含子表统计）！');
  
  console.log('\n📝 测试总结:');
  console.log('✅ 数据统计接口正常（包含子表统计）');
  console.log('✅ Excel导出功能正常（包含子表统计）');
  console.log('✅ 按team_id分组统计正常');
  console.log('✅ 月度统计功能正常（包含子表统计）');
  console.log('✅ 子表查询条件正确（reply_user_id ≠ 5f22591c77d7193df40023a2 且 reply_user_id ≠ release_user_id）');
  console.log('✅ banner_status=1统计功能正常');
  
  if (filePath) {
    console.log(`✅ Excel文件已保存到: ${filePath}`);
  }
  
  console.log('\n📊 Excel文件包含以下工作表:');
  console.log('- 2025年咨询数据: 原始数据详情(_id, create_time, consult_title, team_id, open_status, second_count)');
  console.log('- Team统计: 按team_id分组统计（包含子表记录率和banner_status=1统计）');
  console.log('- 月度Team统计: 按月份和team_id统计（包含子表统计和banner_status=1统计）');
}

// 简单测试
async function runSimpleTest() {
  console.log('🧪 运行简单导出测试（包含子表统计）...\n');
  
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('📊 获取2025年数据统计（包含子表）:');
  await testConsultStatsWithSecond();
  
  console.log('\n✅ 简单测试完成！');
  console.log('💡 如需下载Excel文件，请运行: node test-excel-with-second.js full');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  const testType = process.argv[2] || 'simple';
  
  if (testType === 'full') {
    runFullTest().catch(console.error);
  } else if (testType === 'download') {
    testExportExcelWithSecond().catch(console.error);
  } else {
    runSimpleTest().catch(console.error);
  }
}

module.exports = {
  testConsultStatsWithSecond,
  testExportExcelWithSecond,
  showTestDataInstructions,
  runFullTest,
  runSimpleTest
};
