const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());

// 内存数据存储（用于演示，实际生产环境应使用数据库）
let memoryDatabase = [];
let nextId = 1;

// 生成随机数据的函数
function generateRandomData(id, count) {
  const data = [];
  for (let i = 0; i < count; i++) {
    data.push({
      _id: nextId++,
      id: id,
      value: Math.floor(Math.random() * 1000) + 1, // 1-1000的随机数
      timestamp: new Date(),
      metadata: {
        index: i + 1,
        batch: true
      }
    });
  }
  return data;
}

// 批量插入数据的接口
app.post('/api/batch-insert', async (req, res) => {
  try {
    const { ids } = req.body;
    
    // 验证输入
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'ids参数必须是非空数组'
      });
    }

    console.log(`📝 开始批量插入数据，ID数量: ${ids.length}`);
    
    const allData = [];
    const insertSummary = [];

    // 为每个ID生成随机数量的数据
    for (const id of ids) {
      const randomCount = Math.floor(Math.random() * 201) + 300; // 300-500之间的随机数
      const dataForId = generateRandomData(id, randomCount);
      allData.push(...dataForId);
      
      insertSummary.push({
        id: id,
        count: randomCount
      });
      
      console.log(`📊 ID: ${id} - 生成 ${randomCount} 条数据`);
    }

    // 批量插入到内存数据库
    const startTime = Date.now();
    memoryDatabase.push(...allData);
    const endTime = Date.now();
    
    console.log(`✅ 批量插入完成，总计 ${allData.length} 条数据，耗时 ${endTime - startTime}ms`);

    res.json({
      success: true,
      message: '批量插入成功',
      data: {
        totalInserted: allData.length,
        insertSummary: insertSummary,
        executionTime: `${endTime - startTime}ms`
      }
    });

  } catch (error) {
    console.error('❌ 批量插入失败:', error);
    res.status(500).json({
      success: false,
      message: '批量插入失败',
      error: error.message
    });
  }
});

// 查询数据的接口（用于验证插入结果）
app.get('/api/data/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const data = memoryDatabase
      .filter(item => item.id === id)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    res.json({
      success: true,
      data: {
        id: id,
        count: data.length,
        records: data
      }
    });
  } catch (error) {
    console.error('❌ 查询数据失败:', error);
    res.status(500).json({
      success: false,
      message: '查询数据失败',
      error: error.message
    });
  }
});

// 获取数据统计的接口
app.get('/api/stats', async (req, res) => {
  try {
    const totalCount = memoryDatabase.length;
    const uniqueIds = [...new Set(memoryDatabase.map(item => item.id))];
    
    // 计算每个ID的统计信息
    const idStats = {};
    memoryDatabase.forEach(item => {
      if (!idStats[item.id]) {
        idStats[item.id] = {
          _id: item.id,
          count: 0,
          latestTimestamp: item.timestamp
        };
      }
      idStats[item.id].count++;
      if (new Date(item.timestamp) > new Date(idStats[item.id].latestTimestamp)) {
        idStats[item.id].latestTimestamp = item.timestamp;
      }
    });

    const idStatistics = Object.values(idStats)
      .sort((a, b) => new Date(b.latestTimestamp) - new Date(a.latestTimestamp));

    res.json({
      success: true,
      data: {
        totalRecords: totalCount,
        uniqueIds: uniqueIds.length,
        idStatistics: idStatistics
      }
    });
  } catch (error) {
    console.error('❌ 获取统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

// 清空数据的接口（仅用于测试）
app.delete('/api/clear', (req, res) => {
  const beforeCount = memoryDatabase.length;
  memoryDatabase = [];
  nextId = 1;
  
  console.log(`🗑️ 清空数据库，删除了 ${beforeCount} 条记录`);
  
  res.json({
    success: true,
    message: `成功清空数据库，删除了 ${beforeCount} 条记录`
  });
});

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Node.js服务运行正常（内存模式）',
    timestamp: new Date().toISOString(),
    database: '内存数据库',
    totalRecords: memoryDatabase.length
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器启动成功，端口: ${PORT}`);
  console.log(`💾 使用内存数据库模式（数据不会持久化）`);
  console.log(`📍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📍 批量插入接口: POST http://localhost:${PORT}/api/batch-insert`);
  console.log(`📍 查询数据接口: GET http://localhost:${PORT}/api/data/:id`);
  console.log(`📍 统计数据接口: GET http://localhost:${PORT}/api/stats`);
  console.log(`📍 清空数据接口: DELETE http://localhost:${PORT}/api/clear`);
});

module.exports = app;
