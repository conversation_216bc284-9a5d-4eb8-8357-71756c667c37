const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testMonthlyReport() {
  console.log('🧪 开始测试月度报表功能...\n');

  try {
    // 1. 检查月度报表定时任务状态
    console.log('1️⃣ 检查月度报表定时任务状态...');
    const statusResponse = await axios.get(`${BASE_URL}/api/cron-status`);
    console.log('📊 定时任务状态:', statusResponse.data);
    console.log('');

    // 2. 启动月度报表定时任务（如果未启动）
    if (!statusResponse.data.data.monthlyReportJob.isRunning) {
      console.log('2️⃣ 启动月度报表定时任务...');
      const startResponse = await axios.post(`${BASE_URL}/api/monthly-report-control`, {
        action: 'start'
      });
      console.log('✅ 启动结果:', startResponse.data);
      console.log('');
    } else {
      console.log('2️⃣ 月度报表定时任务已在运行中');
      console.log('');
    }

    // 3. 手动发送月度报表测试
    console.log('3️⃣ 手动发送月度报表测试...');
    console.log('⚠️  注意：这将生成Excel文件并尝试发送邮件');
    console.log('📧 请确保已配置正确的邮箱设置');
    
    // 询问用户是否继续
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise((resolve) => {
      rl.question('是否继续发送测试邮件？(y/n): ', (answer) => {
        rl.close();
        resolve(answer.toLowerCase());
      });
    });

    if (answer === 'y' || answer === 'yes') {
      console.log('📧 开始发送测试邮件...');
      const sendResponse = await axios.post(`${BASE_URL}/api/send-monthly-report`);
      console.log('✅ 发送结果:', sendResponse.data);
    } else {
      console.log('⏭️  跳过邮件发送测试');
    }

    console.log('');

    // 4. 再次检查状态
    console.log('4️⃣ 最终状态检查...');
    const finalStatusResponse = await axios.get(`${BASE_URL}/api/cron-status`);
    console.log('📊 最终状态:', finalStatusResponse.data);

    console.log('\n✅ 月度报表功能测试完成！');
    
    // 显示功能说明
    console.log('\n📋 月度报表功能说明:');
    console.log('- 📅 定时执行: 每月最后一天23:30自动发送');
    console.log('- 📊 报表内容: 2025年全年数据统计');
    console.log('- 📧 邮件发送: 发送到配置的QQ邮箱');
    console.log('- 🏷️  团队映射: 显示中文团队名称');
    console.log('- 📈 统计维度: 包含子表统计和banner_status统计');
    
    console.log('\n🔧 环境变量配置:');
    console.log('- EMAIL_USER: 发送方QQ邮箱');
    console.log('- EMAIL_PASS: QQ邮箱授权码');
    console.log('- RECIPIENT_EMAIL: 接收方邮箱');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 显示邮箱配置说明
function showEmailConfigInstructions() {
  console.log('📧 邮箱配置说明:');
  console.log('');
  console.log('1. 设置环境变量:');
  console.log('   EMAIL_USER=<EMAIL>');
  console.log('   EMAIL_PASS=your-email-authorization-code');
  console.log('   RECIPIENT_EMAIL=<EMAIL>');
  console.log('');
  console.log('2. QQ邮箱授权码获取:');
  console.log('   - 登录QQ邮箱 -> 设置 -> 账户');
  console.log('   - 开启SMTP服务');
  console.log('   - 生成授权码（不是QQ密码）');
  console.log('');
  console.log('3. 或者在代码中直接修改EMAIL_CONFIG配置');
  console.log('');
}

// 主函数
async function main() {
  console.log('🎯 月度报表功能测试工具\n');
  
  // 显示配置说明
  showEmailConfigInstructions();
  
  // 运行测试
  await testMonthlyReport();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testMonthlyReport,
  showEmailConfigInstructions
};
