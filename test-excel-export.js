const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';

// 测试获取2025年统计数据
async function testConsultStats2025() {
  try {
    console.log('📊 获取2025年咨询数据统计...');
    
    const response = await axios.get(`${BASE_URL}/api/consult-stats-2025`);
    
    console.log('✅ 2025年数据统计结果:');
    console.log('');
    
    const data = response.data.data;
    
    // 显示总体统计
    console.log('📈 总体统计:');
    console.log(`   总数据量: ${data.summary.totalCount} 条`);
    console.log(`   已生成点击记录: ${data.summary.generatedCount} 条 (${data.summary.generatedPercentage})`);
    console.log(`   待生成点击记录: ${data.summary.pendingCount} 条`);
    console.log(`   统计年份: ${data.summary.year}`);
    console.log(`   日期范围: ${data.summary.dateRange.start} 至 ${data.summary.dateRange.end}`);
    console.log('');
    
    // 显示Team统计（前10个）
    console.log('🏢 Team统计 (前10个):');
    data.topTeams.forEach((team, index) => {
      console.log(`   ${index + 1}. Team ID: ${team.teamId}`);
      console.log(`      总数: ${team.totalCount} 条 (${team.percentage})`);
      console.log(`      已生成: ${team.generatedCount} 条 (${team.generatedPercentage})`);
      console.log('');
    });
    
    // 显示月度统计
    console.log('📅 月度统计:');
    data.monthlyStats.forEach(month => {
      console.log(`   ${month.month}: ${month.count} 条`);
    });
    
    return response.data;
  } catch (error) {
    console.error('❌ 获取统计数据失败:', error.response?.data || error.message);
  }
}

// 测试下载Excel文件
async function testExportExcel() {
  try {
    console.log('📥 开始下载2025年咨询数据Excel文件...');
    
    const response = await axios.get(`${BASE_URL}/api/export-consult-2025`, {
      responseType: 'stream'
    });
    
    // 获取文件名
    const contentDisposition = response.headers['content-disposition'];
    let fileName = 'xqbmt_consult_2025.xlsx';
    
    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (fileNameMatch && fileNameMatch[1]) {
        fileName = decodeURIComponent(fileNameMatch[1].replace(/['"]/g, ''));
      }
    }
    
    // 保存文件
    const downloadPath = path.join(__dirname, 'downloads');
    if (!fs.existsSync(downloadPath)) {
      fs.mkdirSync(downloadPath, { recursive: true });
    }
    
    const filePath = path.join(downloadPath, fileName);
    const writer = fs.createWriteStream(filePath);
    
    response.data.pipe(writer);
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log(`✅ Excel文件下载成功: ${filePath}`);
        
        // 检查文件大小
        const stats = fs.statSync(filePath);
        console.log(`📁 文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
        
        resolve(filePath);
      });
      
      writer.on('error', (error) => {
        console.error('❌ 文件写入失败:', error);
        reject(error);
      });
    });
    
  } catch (error) {
    console.error('❌ 下载Excel文件失败:', error.response?.data || error.message);
  }
}

// 创建测试数据的说明
function showTestDataInstructions() {
  console.log('💡 创建2025年测试数据说明:');
  console.log('');
  console.log('1. 创建不同team_id的2025年咨询数据:');
  console.log(`
  // Team A 的数据
  db.xqbmt_consult.insertMany([
    {
      team_id: "team_001",
      open_status: 1,
      create_time: new Date("2025-01-15T10:30:00.000Z"),
      update_time: new Date()
    },
    {
      team_id: "team_001", 
      open_status: 1,
      create_time: new Date("2025-02-20T14:20:00.000Z"),
      update_time: new Date()
    }
  ])
  
  // Team B 的数据
  db.xqbmt_consult.insertMany([
    {
      team_id: "team_002",
      open_status: 1,
      create_time: new Date("2025-03-10T09:15:00.000Z"),
      update_time: new Date()
    },
    {
      team_id: "team_002",
      open_status: 1,
      create_time: new Date("2025-04-05T16:45:00.000Z"),
      update_time: new Date(),
      clicks_generated: new Date(),
      clicks_count: 456
    }
  ])
  
  // 无team_id的数据
  db.xqbmt_consult.insertOne({
    open_status: 1,
    create_time: new Date("2025-05-12T11:30:00.000Z"),
    update_time: new Date()
  })
  `);
  
  console.log('2. 查看2025年数据:');
  console.log(`
  db.xqbmt_consult.find({
    create_time: {
      $gte: new Date("2025-01-01T00:00:00.000Z"),
      $lte: new Date("2025-12-31T23:59:59.999Z")
    }
  }).pretty()
  `);
  
  console.log('3. 按team_id统计:');
  console.log(`
  db.xqbmt_consult.aggregate([
    {
      $match: {
        create_time: {
          $gte: new Date("2025-01-01T00:00:00.000Z"),
          $lte: new Date("2025-12-31T23:59:59.999Z")
        }
      }
    },
    {
      $group: {
        _id: "$team_id",
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    }
  ])
  `);
}

// 运行完整测试
async function runFullTest() {
  console.log('🚀 开始测试2025年数据导出功能...\n');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 1. 获取统计数据
  console.log('1️⃣ 获取2025年数据统计');
  const statsResult = await testConsultStats2025();
  console.log('\n' + '='.repeat(80) + '\n');
  
  // 2. 下载Excel文件
  console.log('2️⃣ 下载Excel文件');
  const filePath = await testExportExcel();
  console.log('\n' + '='.repeat(80) + '\n');
  
  // 3. 显示测试数据创建说明
  console.log('3️⃣ 测试数据创建说明');
  showTestDataInstructions();
  
  console.log('\n🎉 2025年数据导出功能测试完成！');
  
  console.log('\n📝 测试总结:');
  console.log('✅ 数据统计接口正常');
  console.log('✅ Excel导出功能正常');
  console.log('✅ 按team_id分组统计正常');
  console.log('✅ 月度统计功能正常');
  
  if (filePath) {
    console.log(`✅ Excel文件已保存到: ${filePath}`);
  }
  
  console.log('\n📊 Excel文件包含以下工作表:');
  console.log('- 2025年咨询数据: 原始数据详情');
  console.log('- Team统计: 按team_id分组统计');
  console.log('- 月度统计: 按月份统计新增数据');
}

// 简单测试
async function runSimpleTest() {
  console.log('🧪 运行简单导出测试...\n');
  
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log('📊 获取2025年数据统计:');
  await testConsultStats2025();
  
  console.log('\n✅ 简单测试完成！');
  console.log('💡 如需下载Excel文件，请运行: node test-excel-export.js full');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  const testType = process.argv[2] || 'simple';
  
  if (testType === 'full') {
    runFullTest().catch(console.error);
  } else if (testType === 'download') {
    testExportExcel().catch(console.error);
  } else {
    runSimpleTest().catch(console.error);
  }
}

module.exports = {
  testConsultStats2025,
  testExportExcel,
  showTestDataInstructions,
  runFullTest,
  runSimpleTest
};
