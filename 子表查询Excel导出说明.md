# 子表查询Excel导出功能说明

## 🎯 功能概述

新增了2025年xqbmt_consult数据的Excel导出功能，包含子表xqbmt_consult_second的查询统计：
- 导出2025年全年的咨询数据到Excel文件
- 按team_id分组统计各团队的数据量
- 按月份统计2025年新增数据趋势
- 统计每条咨询记录在子表中的记录数（reply_user_id ≠ 5f22591c77d7193df40023a2）
- 统计各月份banner_status=1的数量，按team_id区分
- 提供详细的统计信息API

## 📊 API 接口

### 1. 导出Excel文件（包含子表统计）
```http
GET /api/export-consult-2025-with-second
```

**功能说明:**
- 导出2025年(2025-01-01 至 2025-12-31)的所有咨询数据
- 查询子表xqbmt_consult_second中符合条件的记录数
- 生成包含3个工作表的Excel文件
- 自动下载文件到浏览器

**响应:**
- 成功：返回Excel文件下载
- 失败：返回JSON错误信息

### 2. 获取统计数据（包含子表统计）
```http
GET /api/consult-stats-2025-with-second
```

**功能说明:**
- 获取2025年咨询数据的详细统计信息
- 包含子表记录统计
- 不下载文件，只返回JSON数据

**响应示例:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalCount": 1250,
      "generatedCount": 890,
      "withSecondCount": 750,
      "bannerStatus1Count": 420,
      "pendingCount": 360,
      "generatedPercentage": "71.20%",
      "withSecondPercentage": "60.00%",
      "bannerStatus1Percentage": "33.60%",
      "year": "2025",
      "dateRange": {
        "start": "2025-01-01",
        "end": "2025-12-31"
      }
    },
    "teamStats": [
      {
        "teamId": "team_001",
        "totalCount": 450,
        "generatedCount": 320,
        "withSecondCount": 280,
        "bannerStatus1Count": 150,
        "percentage": "36.00%",
        "generatedPercentage": "71.11%",
        "withSecondPercentage": "62.22%",
        "bannerStatus1Percentage": "33.33%"
      }
    ],
    "monthlyStats": [
      {
        "month": "2025-01",
        "team_001_total": 120,
        "team_001_withSecond": 80,
        "team_001_bannerStatus1": 40,
        "team_002_total": 90,
        "team_002_withSecond": 60,
        "team_002_bannerStatus1": 30,
        "total": 210,
        "withSecondTotal": 140,
        "bannerStatus1Total": 70
      }
    ]
  }
}
```

## 📋 Excel文件结构

### 工作表1: 2025年咨询数据
包含原始数据的详细信息：
- **_id**: 记录的唯一标识
- **create_time**: 记录创建时间（年月日时分秒格式）
- **consult_title**: 咨询标题
- **team_id**: 团队标识
- **open_status**: 开放状态
- **second_count**: 子表中符合条件的记录数

### 工作表2: Team统计
按team_id分组的统计信息：
- **Team ID**: 团队标识
- **总数据条数**: 该团队的数据总量
- **有子表记录条数**: 该团队中有子表记录的数据量
- **banner_status=1条数**: 该团队中banner_status=1的数据量
- **子表记录率**: 有子表记录的百分比
- **banner_status=1比例**: banner_status=1的百分比
- **占比**: 该团队数据占总数据的百分比
- **总计行**: 显示所有数据的汇总

### 工作表3: 月度Team统计
按月份和team_id统计的新增数据：
- **月份**: YYYY-MM格式
- **各team_id列**: 每个团队在该月的数据量、子表记录量和banner_status=1数量
- **月度总计**: 该月所有团队的数据总和
- **月度有子表总计**: 该月所有团队的子表记录总和
- **月度banner状态1总计**: 该月所有团队的banner_status=1总和
- **总计行**: 显示每个团队的年度总计

## 🔍 子表查询条件

### 查询条件
```javascript
{
  consult_id: { $in: consultIds },
  reply_user_id: { $ne: '5f22591c77d7193df40023a2' }
}
```

### 统计逻辑
1. 查询主表xqbmt_consult中2025年的所有数据
2. 提取所有咨询ID
3. 查询子表xqbmt_consult_second中符合条件的记录
   - consult_id在主表ID列表中
   - reply_user_id不等于5f22591c77d7193df40023a2
4. 统计每个咨询ID的子表记录数
5. 在Excel中显示统计结果

## 🧪 测试方法

### 1. 运行测试脚本
```bash
# 简单测试（只获取统计数据）
node test-excel-with-second.js

# 完整测试（包含Excel下载）
node test-excel-with-second.js full

# 只测试下载功能
node test-excel-with-second.js download
```

### 2. 手动测试
```bash
# 获取统计数据
curl http://localhost:3000/api/consult-stats-2025-with-second

# 下载Excel文件
curl -O -J http://localhost:3000/api/export-consult-2025-with-second
```

### 3. 浏览器测试
直接访问：
- 统计数据：`http://localhost:3000/api/consult-stats-2025-with-second`
- 下载Excel：`http://localhost:3000/api/export-consult-2025-with-second`

## 📝 创建测试数据

### 创建主表数据
```javascript
db.xqbmt_consult.insertMany([
  {
    _id: ObjectId("60f1234567890123456789a1"),
    team_id: "team_001",
    open_status: 1,
    banner_status: 1,
    consult_title: "咨询标题1",
    create_time: new Date("2025-01-15T10:30:00.000Z"),
    update_time: new Date()
  },
  {
    _id: ObjectId("60f1234567890123456789a2"),
    team_id: "team_001",
    open_status: 1,
    banner_status: 0,
    consult_title: "咨询标题2",
    create_time: new Date("2025-02-20T14:20:00.000Z"),
    update_time: new Date()
  }
])
```

### 创建子表数据
```javascript
db.xqbmt_consult_second.insertMany([
  {
    consult_id: "60f1234567890123456789a1",
    reply_user_id: "user001",  // 不等于 5f22591c77d7193df40023a2
    create_time: new Date("2025-01-15T11:00:00.000Z"),
    update_time: new Date()
  },
  {
    consult_id: "60f1234567890123456789a1",
    reply_user_id: "user002",  // 不等于 5f22591c77d7193df40023a2
    create_time: new Date("2025-01-15T12:00:00.000Z"),
    update_time: new Date()
  },
  {
    consult_id: "60f1234567890123456789a2",
    reply_user_id: "5f22591c77d7193df40023a2",  // 等于指定值，不会被统计
    create_time: new Date("2025-02-20T15:00:00.000Z"),
    update_time: new Date()
  }
])
```

### 查询验证
```javascript
// 统计每个consult_id的子表记录数
db.xqbmt_consult_second.aggregate([
  {
    $match: {
      reply_user_id: { $ne: "5f22591c77d7193df40023a2" }
    }
  },
  {
    $group: {
      _id: "$consult_id",
      count: { $sum: 1 }
    }
  }
])
```

## ⚙️ 技术实现

### 依赖库
- **xlsx**: Excel文件生成和处理
- **mongoose**: MongoDB数据库操作
- **fs**: 文件系统操作
- **path**: 路径处理

### 数据处理流程
1. 查询主表数据
2. 查询子表数据
3. 统计每个咨询ID的子表记录数
4. 按team_id和月份进行统计
5. 生成Excel文件
6. 发送文件到客户端

### 性能优化
- 使用MongoDB聚合查询优化性能
- 批量查询子表数据
- 使用内存映射优化统计计算
- 临时文件自动清理避免磁盘空间占用

## 🔒 安全注意事项

- 导出功能不需要身份验证（根据需要可添加）
- 临时文件自动清理，避免敏感数据泄露
- 只导出指定字段，不包含敏感信息
- 文件名包含日期，避免缓存问题

## 📈 使用场景

1. **数据分析**: 分析各团队的咨询数据量和回复情况
2. **报表生成**: 生成月度、季度业务报表
3. **业务监控**: 监控咨询回复的完成情况
4. **数据备份**: 定期导出数据进行备份
5. **跨部门协作**: 提供Excel格式便于其他部门使用
