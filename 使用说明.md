# Node.js MongoDB 批量数据插入服务 - 使用说明

## 项目概述

这是一个Node.js服务，提供批量数据插入功能。根据传入的ID数组，为每个ID随机插入300-500条数据到数据库中。

## 文件说明

- `server.js` - 完整版服务器（需要MongoDB）
- `server-memory.js` - 内存版服务器（无需外部数据库，用于演示）
- `test-memory.js` - API测试脚本
- `.env` - 环境配置文件
- `package.json` - 项目依赖配置

## 快速开始

### 1. 启动内存版服务器（推荐用于测试）
```bash
node server-memory.js
```

### 2. 运行测试
```bash
node test-memory.js
```

## API接口详情

### 批量插入数据
```http
POST /api/batch-insert
Content-Type: application/json

{
  "ids": ["user001", "user002", "user003"]
}
```

**功能说明：**
- 接收ID数组
- 为每个ID随机生成300-500条数据
- 每条数据包含：ID、随机数值(1-1000)、时间戳、元数据

**响应示例：**
```json
{
  "success": true,
  "message": "批量插入成功",
  "data": {
    "totalInserted": 1247,
    "insertSummary": [
      {"id": "user001", "count": 423},
      {"id": "user002", "count": 387},
      {"id": "user003", "count": 437}
    ],
    "executionTime": "2ms"
  }
}
```

### 查询数据
```http
GET /api/data/:id
```

**功能说明：**
- 查询指定ID的所有数据
- 按时间戳倒序排列

### 统计信息
```http
GET /api/stats
```

**功能说明：**
- 返回总记录数
- 返回唯一ID数量
- 返回每个ID的统计信息

### 健康检查
```http
GET /health
```

### 清空数据（仅内存版）
```http
DELETE /api/clear
```

## 测试结果示例

运行 `node test-memory.js` 的输出：

```
🚀 开始运行API测试（内存模式）...

🏥 测试健康检查...
✅ 健康检查成功

🧪 测试批量插入API...
✅ 批量插入测试成功:
- 总插入数据: 1942 条
- user001: 457 条数据
- user002: 480 条数据
- user003: 300 条数据
- user004: 365 条数据
- user005: 340 条数据

🔍 测试查询数据...
✅ 查询 user001 成功，数据量: 457

📊 测试统计数据...
✅ 统计数据获取成功:
- 总记录数: 2746
- 唯一ID数: 7

🎉 所有测试完成！
```

## 数据结构

每条插入的数据结构：
```json
{
  "_id": 1,
  "id": "user001",
  "value": 844,
  "timestamp": "2025-06-27T06:11:34.123Z",
  "metadata": {
    "index": 1,
    "batch": true
  }
}
```

## 特性验证

✅ **随机数量生成**: 每个ID生成300-500条随机数量的数据  
✅ **随机数值**: 每条数据的value字段是1-1000的随机数  
✅ **批量插入**: 支持一次性插入多个ID的数据  
✅ **数据查询**: 可以查询指定ID的所有数据  
✅ **统计功能**: 提供数据统计和汇总信息  
✅ **错误处理**: 完整的错误处理和验证机制  

## 生产环境部署

如需在生产环境使用，请：
1. 启动MongoDB服务
2. 修改 `.env` 文件中的数据库连接字符串
3. 使用 `node server.js` 启动完整版服务器

## 注意事项

- 内存版服务器数据不会持久化，重启后数据丢失
- 完整版服务器需要MongoDB服务运行
- 每次批量插入的数据量在300-500之间随机
- 服务器默认运行在3000端口
